# Quick Start Guide

Get up and running with the SWE-bench AI Evaluator in 5 minutes.

## Prerequisites

- Python 3.9+
- Git
- API keys for the AI services you want to test

## Installation

1. **Clone and install**:
```bash
git clone <repository-url>
cd swe-bench-evaluator
pip install -r requirements.txt
```

2. **Set up environment**:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Verify setup**:
```bash
python test_basic_functionality.py
```

## API Keys Setup

### Required Keys

Get these API keys and add them to your `.env` file:

1. **Anthropic (<PERSON>)**:
   - Go to https://console.anthropic.com/
   - Create API key
   - Add to `.env`: `ANTHROPIC_API_KEY=your_key_here`

2. **OpenAI (Codex)**:
   - Go to https://platform.openai.com/api-keys
   - Create API key
   - Add to `.env`: `OPENAI_API_KEY=your_key_here`

3. **E2B (Sandboxes)**:
   - Go to https://e2b.dev/
   - Create account and get API key
   - Add to `.env`: `E2B_API_KEY=your_key_here`

### Optional: Codex CLI

For Codex evaluations, install the CLI:
```bash
# Follow OpenAI's Codex CLI installation guide
# This is required for Codex evaluations
```

## First Evaluation

Run your first evaluation with Claude on 5 tasks:

```bash
python cli.py evaluate --model claude --examples 5 --budget 10.0
```

This will:
- Download SWE-bench lite dataset
- Evaluate 5 tasks using Claude
- Run tests in secure sandboxes
- Generate a comparison report
- Stay within $10 budget

## Understanding Results

The evaluation will show:

```
Model     Tasks  Completed  Success Rate  Avg Time  Total Cost
Claude    5      4/5        75.0%         45.2s     $3.45
```

- **Success Rate**: Percentage of tasks where all tests pass correctly
- **Completion Rate**: Tasks completed without errors
- **Cost**: Total API usage cost

## Next Steps

### Compare Multiple Models

```bash
# Compare Claude and Codex
python cli.py evaluate --model all --examples 10 --workers 3
```

### Filter by Repository

```bash
# Only evaluate Django tasks
python cli.py evaluate --model claude --examples 15 --repos "django/django"
```

### Scale Up

```bash
# Larger evaluation
python cli.py evaluate --model all --examples 50 --workers 5 --budget 50.0
```

### Use Modal for Scale

```bash
# Install Modal
pip install modal

# Set up Modal secrets
modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key
modal secret create openai-api-key OPENAI_API_KEY=your_key  
modal secret create e2b-api-key E2B_API_KEY=your_key

# Run on Modal
modal run modal_deployment.py --max-tasks 100 --evaluators claude,codex
```

## Troubleshooting

### Common Issues

1. **"No API key" errors**:
   - Check your `.env` file
   - Ensure keys are valid and have credits

2. **"E2B connection failed"**:
   - Verify E2B API key
   - Check internet connection
   - Try Docker fallback: set `USE_E2B=false` in `.env`

3. **"Codex CLI not found"**:
   - Install Codex CLI from OpenAI
   - Or skip Codex: `--model claude`

4. **Budget exceeded**:
   - Increase budget: `--budget 20.0`
   - Reduce examples: `--examples 10`

### Getting Help

1. **Check logs**: `tail -f logs/evaluation.log`
2. **Run diagnostics**: `python cli.py setup`
3. **Test basic functionality**: `python test_basic_functionality.py`

## Understanding SWE-bench

SWE-bench evaluates AI tools on real GitHub issues:

- **FAIL_TO_PASS tests**: Should fail before fix, pass after
- **PASS_TO_PASS tests**: Should pass both before and after
- **Deterministic**: Based on actual test execution, not AI judgment

Success means the AI correctly implemented the GitHub issue fix.

## Cost Management

Typical costs per task:
- Claude: $0.02-0.10
- Codex: $0.05-0.15  
- CodeGen: $0.03-0.08

Budget recommendations:
- Testing (5-10 tasks): $5-10
- Small comparison (20 tasks): $20-30
- Full evaluation (50+ tasks): $50-100

The framework automatically stops when budget is exceeded.

## What's Next?

Once you have basic evaluations working:

1. **Analyze Results**: Use `python cli.py analyze results_file.json`
2. **Custom Filters**: Filter by specific repositories or issue types
3. **Scale Up**: Use Modal for large-scale evaluations
4. **Integrate**: Use the Python API in your own projects

Happy evaluating! 🚀
