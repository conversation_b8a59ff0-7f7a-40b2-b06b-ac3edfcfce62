"""Command-line interface for SWE-bench evaluation."""

import asyncio
import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from typing import List, Optional

from src.evaluator import SWEBenchEvaluator
from src.models import EvaluatorType
from src.config import config
from src.utils import logger

console = Console()


def print_banner():
    """Print application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    SWE-bench AI Evaluator                   ║
║              Compare Claude, Codex, and CodeGen             ║
╚══════════════════════════════════════════════════════════════╝
    """
    console.print(banner, style="bold blue")


@click.group()
def cli():
    """SWE-bench AI Code Evaluator - Compare different AI coding tools."""
    print_banner()


@cli.command()
@click.option('--model', 
              type=click.Choice(['claude', 'codex', 'codegen', 'all']),
              default='claude',
              help='AI model to evaluate')
@click.option('--examples', 
              type=int, 
              default=config.default_examples,
              help='Number of examples to evaluate')
@click.option('--workers', 
              type=int, 
              default=config.default_workers,
              help='Number of concurrent workers')
@click.option('--subset',
              type=click.Choice(['lite', 'verified', 'full']),
              default='lite',
              help='SWE-bench dataset subset')
@click.option('--repos',
              help='Comma-separated list of repositories to filter by')
@click.option('--budget',
              type=float,
              default=config.max_budget,
              help='Maximum budget in USD')
def evaluate(model: str, examples: int, workers: int, subset: str, 
            repos: Optional[str], budget: float):
    """Evaluate AI models on SWE-bench tasks."""
    
    # Parse repositories filter
    filter_repos = None
    if repos:
        filter_repos = [repo.strip() for repo in repos.split(',')]
    
    # Parse evaluator types
    if model == 'all':
        evaluator_types = [EvaluatorType.CLAUDE, EvaluatorType.CODEX, EvaluatorType.CODEGEN]
    else:
        evaluator_type_map = {
            'claude': EvaluatorType.CLAUDE,
            'codex': EvaluatorType.CODEX,
            'codegen': EvaluatorType.CODEGEN
        }
        evaluator_types = [evaluator_type_map[model]]
    
    # Update budget
    config.max_budget = budget
    
    console.print(f"\n[bold green]Starting Evaluation[/bold green]")
    console.print(f"Model(s): {', '.join(e.value for e in evaluator_types)}")
    console.print(f"Examples: {examples}")
    console.print(f"Workers: {workers}")
    console.print(f"Subset: {subset}")
    console.print(f"Budget: ${budget:.2f}")
    if filter_repos:
        console.print(f"Repositories: {', '.join(filter_repos)}")
    console.print()
    
    # Run evaluation
    asyncio.run(_run_evaluation(
        evaluator_types, examples, workers, subset, filter_repos
    ))


async def _run_evaluation(evaluator_types: List[EvaluatorType],
                         examples: int,
                         workers: int,
                         subset: str,
                         filter_repos: Optional[List[str]]):
    """Run the evaluation asynchronously."""
    
    evaluator = SWEBenchEvaluator()
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Add progress tasks
            load_task = progress.add_task("Loading SWE-bench tasks...", total=None)
            
            # Run evaluation
            report = await evaluator.run_evaluation(
                subset=subset,
                max_tasks=examples,
                evaluator_types=evaluator_types,
                max_concurrent=workers,
                filter_repos=filter_repos
            )
            
            progress.update(load_task, completed=True, description="✅ Evaluation completed!")
        
        # Display results
        _display_results(report)
        
    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {str(e)}")
        logger.error(f"CLI evaluation failed", error=str(e))


def _display_results(report):
    """Display evaluation results in a nice table."""
    
    console.print(f"\n[bold green]Evaluation Results[/bold green]")
    
    # Create results table
    table = Table(title="SWE-bench Evaluation Comparison")
    
    table.add_column("Model", style="cyan", no_wrap=True)
    table.add_column("Tasks", justify="right")
    table.add_column("Completed", justify="right")
    table.add_column("Success Rate", justify="right")
    table.add_column("Avg Time", justify="right")
    table.add_column("Total Cost", justify="right")
    
    for evaluator_type, summary in report.summaries.items():
        table.add_row(
            evaluator_type.value.title(),
            str(summary.total_tasks),
            f"{summary.completed_tasks}/{summary.total_tasks}",
            f"{summary.overall_success_rate:.1%}",
            f"{summary.average_execution_time:.1f}s",
            f"${summary.total_api_cost:.2f}"
        )
    
    console.print(table)
    
    # Show best performers
    console.print(f"\n[bold yellow]Best Performers:[/bold yellow]")
    
    best_success = report.get_best_performer("success_rate")
    best_completion = report.get_best_performer("completion_rate")
    best_cost = report.get_best_performer("cost_efficiency")
    
    if best_success:
        console.print(f"🏆 Highest Success Rate: [bold]{best_success.value}[/bold]")
    if best_completion:
        console.print(f"✅ Highest Completion Rate: [bold]{best_completion.value}[/bold]")
    if best_cost:
        console.print(f"💰 Most Cost Efficient: [bold]{best_cost.value}[/bold]")


@cli.command()
@click.option('--subset',
              type=click.Choice(['lite', 'verified', 'full']),
              default='lite',
              help='SWE-bench dataset subset')
@click.option('--max-tasks',
              type=int,
              default=50,
              help='Maximum tasks to show info for')
def info(subset: str, max_tasks: int):
    """Show information about SWE-bench dataset."""
    
    console.print(f"\n[bold blue]SWE-bench Dataset Information[/bold blue]")
    
    asyncio.run(_show_dataset_info(subset, max_tasks))


async def _show_dataset_info(subset: str, max_tasks: int):
    """Show dataset information."""
    
    from src.swe_bench import SWEBenchLoader
    
    loader = SWEBenchLoader()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        load_task = progress.add_task("Loading dataset...", total=None)
        
        tasks = await loader.load_tasks(subset=subset, max_tasks=max_tasks)
        stats = loader.get_task_statistics(tasks)
        
        progress.update(load_task, completed=True, description="✅ Dataset loaded!")
    
    # Display statistics
    console.print(f"\n[bold green]Dataset Statistics ({subset})[/bold green]")
    console.print(f"Total Tasks: {stats['total_tasks']}")
    console.print(f"Unique Repositories: {stats['unique_repos']}")
    console.print(f"Average Problem Length: {stats['avg_problem_length']:.0f} characters")
    console.print(f"Tasks with Hints: {stats['tasks_with_hints']}")
    console.print(f"Tasks with Tests: {stats['tasks_with_tests']}")
    
    # Show repository distribution
    console.print(f"\n[bold yellow]Repository Distribution (Top 10):[/bold yellow]")
    
    repo_dist = stats['repo_distribution']
    sorted_repos = sorted(repo_dist.items(), key=lambda x: x[1], reverse=True)[:10]
    
    for repo, count in sorted_repos:
        console.print(f"  {repo}: {count} tasks")


@cli.command()
@click.argument('results_file')
def analyze(results_file: str):
    """Analyze saved evaluation results."""
    
    console.print(f"\n[bold blue]Analyzing Results[/bold blue]")
    console.print(f"File: {results_file}")
    
    try:
        from src.swe_bench import ResultValidator
        
        validator = ResultValidator()
        results = validator.load_results(results_file)
        
        console.print(f"\nLoaded {len(results)} results")
        
        # Show basic statistics
        completed = sum(1 for r in results if r.status.value == "completed")
        failed = sum(1 for r in results if r.status.value == "failed")
        total_cost = sum(r.api_cost for r in results)
        
        console.print(f"Completed: {completed}")
        console.print(f"Failed: {failed}")
        console.print(f"Total Cost: ${total_cost:.2f}")
        
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")


@cli.command()
def setup():
    """Setup the evaluation environment."""
    
    console.print(f"\n[bold blue]Environment Setup[/bold blue]")
    
    # Check API keys
    console.print("Checking API keys...")
    
    checks = [
        ("Anthropic API Key", config.anthropic_api_key),
        ("OpenAI API Key", config.openai_api_key),
        ("E2B API Key", config.e2b_api_key)
    ]
    
    for name, key in checks:
        if key and key != "your_api_key_here":
            console.print(f"  ✅ {name}: Configured")
        else:
            console.print(f"  ❌ {name}: Missing")
    
    # Check directories
    console.print("\nChecking directories...")
    
    dirs = [
        ("Dataset Path", config.swe_bench_dataset_path),
        ("Results Path", config.results_output_path),
        ("Log Path", config.log_file.parent)
    ]
    
    for name, path in dirs:
        if path.exists():
            console.print(f"  ✅ {name}: {path}")
        else:
            console.print(f"  ❌ {name}: {path} (will be created)")
    
    console.print(f"\n[bold green]Setup complete![/bold green]")
    console.print("Make sure to set your API keys in the .env file.")


if __name__ == '__main__':
    cli()
