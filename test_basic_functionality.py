#!/usr/bin/env python3
"""Basic functionality test script for SWE-bench evaluator."""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.models import SWEBenchTask, EvaluatorType
from src.swe_bench import SWEBenchLoader
from src.config import config
from src.utils import logger


async def test_task_loading():
    """Test SWE-bench task loading."""
    print("🔄 Testing SWE-bench task loading...")
    
    try:
        loader = SWEBenchLoader()
        
        # Try to load a small number of tasks
        tasks = await loader.load_tasks(subset="lite", max_tasks=5)
        
        if tasks:
            print(f"✅ Successfully loaded {len(tasks)} tasks")
            
            # Show task statistics
            stats = loader.get_task_statistics(tasks)
            print(f"   - Unique repositories: {stats['unique_repos']}")
            print(f"   - Tasks with tests: {stats['tasks_with_tests']}")
            
            # Show first task details
            first_task = tasks[0]
            print(f"   - Sample task: {first_task.instance_id}")
            print(f"   - Repository: {first_task.repo}")
            print(f"   - Problem length: {len(first_task.problem_statement)} chars")
            
            return True
        else:
            print("❌ No tasks loaded")
            return False
            
    except Exception as e:
        print(f"❌ Task loading failed: {str(e)}")
        return False


def test_evaluator_initialization():
    """Test evaluator initialization."""
    print("🔄 Testing evaluator initialization...")
    
    try:
        from src.evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
        
        # Test Claude evaluator (requires API key)
        if config.anthropic_api_key and config.anthropic_api_key != "your_anthropic_api_key_here":
            claude = ClaudeEvaluator()
            print(f"✅ Claude evaluator initialized (rate limit: {claude.get_rate_limit()}/min)")
        else:
            print("⚠️  Claude evaluator skipped (no API key)")
        
        # Test Codex evaluator
        codex = CodexEvaluator()
        print(f"✅ Codex evaluator initialized (rate limit: {codex.get_rate_limit()}/min)")
        
        # Test CodeGen evaluator
        codegen = CodeGenEvaluator()
        print(f"✅ CodeGen evaluator initialized (rate limit: {codegen.get_rate_limit()}/min)")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluator initialization failed: {str(e)}")
        return False


def test_cost_estimation():
    """Test cost estimation."""
    print("🔄 Testing cost estimation...")
    
    try:
        from src.evaluators import ClaudeEvaluator
        
        # Create sample task
        sample_task = SWEBenchTask(
            instance_id="test_task",
            repo="test/repo",
            base_commit="abc123",
            problem_statement="Fix the bug in the function that processes user input",
            hints_text="Check the error handling and validation logic",
            created_at="2024-01-01",
            version="1.0",
            FAIL_TO_PASS=["test_function_fix"],
            PASS_TO_PASS=["test_existing_functionality"]
        )
        
        # Test cost estimation
        if config.anthropic_api_key and config.anthropic_api_key != "your_anthropic_api_key_here":
            claude = ClaudeEvaluator()
            cost = claude.estimate_cost(sample_task)
            print(f"✅ Claude cost estimation: ${cost:.4f}")
            
            if cost > 0 and cost < 1.0:  # Reasonable range
                print("   - Cost estimate looks reasonable")
            else:
                print("   - Warning: Cost estimate may be off")
        else:
            print("⚠️  Cost estimation skipped (no API key)")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost estimation failed: {str(e)}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("🔄 Testing configuration...")
    
    try:
        print(f"✅ Configuration loaded")
        print(f"   - Max budget: ${config.max_budget}")
        print(f"   - Default workers: {config.default_workers}")
        print(f"   - Default examples: {config.default_examples}")
        print(f"   - Dataset path: {config.swe_bench_dataset_path}")
        print(f"   - Results path: {config.results_output_path}")
        
        # Check if directories exist
        if config.swe_bench_dataset_path.exists():
            print("   - Dataset directory exists")
        else:
            print("   - Dataset directory created")
        
        if config.results_output_path.exists():
            print("   - Results directory exists")
        else:
            print("   - Results directory created")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False


def test_logging():
    """Test logging functionality."""
    print("🔄 Testing logging...")
    
    try:
        from src.utils import eval_logger
        
        # Test basic logging
        logger.info("Test log message", test=True)
        eval_logger.task_started("test_task", "claude")
        eval_logger.task_completed("test_task", "claude", 0.8, 10.0, 0.05)
        
        print("✅ Logging system working")
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {str(e)}")
        return False


def test_budget_tracking():
    """Test budget tracking."""
    print("🔄 Testing budget tracking...")
    
    try:
        from src.utils import budget_tracker
        
        # Reset budget tracker
        budget_tracker.max_budget = 10.0
        budget_tracker.current_cost = 0.0
        budget_tracker.cost_by_service = {}
        
        # Test budget operations
        assert budget_tracker.can_afford(5.0) is True
        assert budget_tracker.can_afford(15.0) is False
        
        budget_tracker.add_cost("claude", 3.0)
        assert budget_tracker.current_cost == 3.0
        assert budget_tracker.get_remaining_budget() == 7.0
        
        budget_tracker.add_cost("codex", 2.0)
        assert budget_tracker.current_cost == 5.0
        
        usage_pct = budget_tracker.get_budget_usage_percentage()
        assert usage_pct == 50.0
        
        print("✅ Budget tracking working")
        print(f"   - Current cost: ${budget_tracker.current_cost}")
        print(f"   - Usage: {usage_pct}%")
        print(f"   - Cost breakdown: {budget_tracker.get_cost_breakdown()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Budget tracking test failed: {str(e)}")
        return False


async def main():
    """Run all basic functionality tests."""
    print("🚀 Starting SWE-bench Evaluator Basic Functionality Tests\n")
    
    tests = [
        ("Configuration", test_configuration),
        ("Logging", test_logging),
        ("Budget Tracking", test_budget_tracking),
        ("Evaluator Initialization", test_evaluator_initialization),
        ("Cost Estimation", test_cost_estimation),
        ("Task Loading", test_task_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
        
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic functionality tests passed!")
        print("\nNext steps:")
        print("1. Set up your API keys in .env file")
        print("2. Run: python cli.py setup")
        print("3. Run: python cli.py evaluate --model claude --examples 5")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
