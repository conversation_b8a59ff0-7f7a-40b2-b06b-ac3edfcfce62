
 
Me: Once you create a feature there is no best practice to test the feature. And make sure the code actually works. So what we're doing is after you, let's say, make a feature, now you want to your terminal, do backspace run. What it will do is write the missing test for you. QA the codes for you, that that feature you wrote. K. And then productionize the code for you, like NPM run build. And then yeah, just make sure your code works in production. Once it works, make a PR and then it will you can easily merge it, making, like, vibe coded code production ready. Do you do you okay. Yeah. Yeah. So what I'm getting, right, we are wife, <PERSON>. So how are Vive coded code works in productions? Too long there. Right? Yes. Basically, let's say you make a feature. Okay. From cursor. Right? Okay. How do you know the feature work? What do you do usually? So so I test it, run it, and check Okay. If it's working. Or I maybe try to run unit test if the feature is working. Nice. Yes. You and you and you vibe code these unit tests, right, and all that stuff? Yeah. Okay. So let's say you created one or two unit tests. And it's sort of working locally. Right? Yeah. Now you want to push and merge this vibe code. Right? Likely, if you're trying to productionize this code, you need to write more extensive tests. Also, you need to make sure your code builds properly. And your code is refactored in a modular way. Right? That's the best way to to productionize it. Otherwise, if you just push and merge, okay. Fine. You did one local test. But there could be other ways it fails. Right? And then also the code is probably bloated. Because you didn't go take the time to fix the modularity of the code. You get me? Because it's white coded. Yeah. Right? So our thing after you, you know, white code and then make one or two unit test pass, then it will run another background agent that will basically make the code more modular with best practices for, like, React Next. Js, this is that. And then it will also write missing tests that you haven't thought of. Okay. Or or it will act go and QA the code. Let's say it does NPM run dev and then goes to the the route and then uses stagehand to to get screenshots and actually click on buttons to integrate integration test. Okay. So all these are, like, background agents that will run And once they run, once the tests are done, once the code is modular, it is essentially production ready. Which is then easy for a developer to merge. Right. And then a code that is, like, vibe coded using Cloud Code, but then you are don't have the time to manually test and and, like, update everything. See. The way I see it, like, you guys are fixing VIVE coding. So that it works in productions. Exactly. Basically, it's a vibe coding framework. So it's like vibe testing. Sure. Vibe testing, you can think of it that way. A little bit more than testing. Because you'll test the code, but you also want to make sure your code is clean. So we're gonna, like, it's just essentially another clogged code run. These background agents are cloud code, by the way. These are just, like, asynchronous cloud code. That you run, and it goes and makes your code more modular. So Right? Maybe it's it's splits up, like, let's see let's see your vibe code at component. Puts everything in page dot TSX. Right? All the components and things. Likely, what our back space run will do is analyze that page dot TSX and then instead break up everything into components. And put them in the components folder. That's, like, a way of, like, cleaning up code. Right? Yeah. So it will do that and then make the test pass, and then make sure you can do NPM run build. And then your code looks good according to stagehand. I see. As a browser agent, that will go and check your code. I see. You get it? Is is agents will work as a valid validating agent. We'll validate. Yeah. It will work as a valid agent. Yeah. I see. Got it. Got the sense of what you guys are doing right. So so that's what we're doing. We're not focused We're not competing with or Devin or anything. We are trying to compliment them. So there's the software development life cycle. Right? We're trying to target the testing and deployment life cycle, essentially, part. I see. Yeah. I will send you another doc. Okay. Another document that explains all this, but basically, what we want to do is after we create finish creating our agents, we want to actually create eval datasets and then eval it. Yeah. So Right? Yeah. So eval for doing the evals, do you have output example to taste your evils? Right? For example, you can use LLM as a gel. For for now. Yeah. Yeah. So how do you create the output? Like, do you have all the tests test output? This is the thing. Is LLM as a judge the best way? So if you look at SWE bench, go to SWE bench. Yeah. What they do is how they do their evals is they have previous tests okay. So they have a GitHub issue. Right? They have a GitHub issue. And they want to implement a feature using, like, Cloud Code or like, Cursor or CodeGen. Once you implement the issue, there is a previous test that fails. Like, before you implemented the issue, And then once you implement the issue, the test must pass. That test that failed must pass. If it passed, that means the feature was implemented correctly. I see. You see? That's how they make it deterministic. Not as, like, LLM as a judge. Of the times, LLM as a judge also fails. Because it's too opinionated or, like, do you get what I mean? Yeah. Yeah. Yeah. So I think it would be best if we collect by if you guys collect the real human annotated label dataset. Right? Yes. Yeah. And as of my knowledge, so if it's failed, you can you can you can also give a reasoning model, sample. It will generate synthetic datasets. Yeah. Yeah. So using also, I think Al-Ekram is very good at generating. There's are some libraries for generating synthetic datasets. And you we we will review. Like, for example, I generated some box, right, features. Using the since LLM. Right? And there's a synthetic dataset. And I I do the classifications. So I'll I'll I'll give the meta tags. This is the this problem. This is the this problem. And with. Right? So one way to do this using the synthetic dataset the best way to do is illuminate real world datasets. You can you can collect it from GitHub. I think there are some dataset out there. For now, LLMS JAWS is good. We can also use the we can also implement this. And the best thing is to to the way I see the evils is to look at the data. Like, those custom, like, for example, they have their own matrices like but based on the problem you guys solving, we have to define. We have to make our own matrices. Like if it's passing the arrow. Right? So for example, accuracy, toxicity for REG. It doesn't work in real world. Right? So I think we have to find what is our goal in this goal. Golden datasets or golden standard. Or golden accuracy or matrices. We will implement those matrices based on the experience we have, what you guys have, and we'll also use the the traditional matrices and also custom matrices. And Okay. If I know that, what should be the ideal the most ideal without five coding? So for example, forget the vive coding. Right? How what is the most accurate way to taste We have example. For example, the previous all the unit testing and it passed. Right? We can collect those datasets. Before p l l p l l m p l l m datasets test cases. You use those as a matrices. To see how vibe coding is passing there. Right? Okay. For example, let's say your input is let's say this is your input. Right? It is a Vibe coded feature that kind of works. And then the output is that vibe coded feature that is fully production ready. Okay. Those are the inputs and the outputs. So now, how do you best verify this? How do you yeah. Basically, how do you best verify this and how do you collect this data is gonna be the task for whoever, like, takes this job. Right? So that's it. I see. Okay. So do you think does this sound interesting to you? Of course. Okay. So so if so, before I introduce you to Roland, I'm a there basically, I'm a there j I just do a take home. So that it's easier for you as well. You don't have to, like, waste your time. Yeah. Yeah. If you do a take home where you evaluate or run sweepench, on clot code, codex, maybe CodeGen and CodeLand Devon, or, like, maybe two or three. Right? I I don't have with Devin and other stuffs. Cursor plotter. Yeah. It's good. Okay. Cursor, plot code. Okay. If you it's kind of hard to make, like, cursor work on these. Maybe use, like, CodeGen has an API. Okay? You can use that API. It's pretty cheap. And then yeah, clot code. You don't need to do so many first of all, make sure you don't spend much. Like, max, spend, like, like, I don't know, $100. You know? And then I I will just give you back that money. Whatever it Okay. Okay. Max max spend that. You don't need to run full SuiVenture Lite. That's, like, a thousand dollars. I see. You get me? So if you even run, like, 20, 30 examples, and then you have it set up so that if you run, like, I don't know, SWE bench dash dash Claude. Sweet bench dash dash codex, Okay. By the way, codex, you do have access. You can use the CLI. Yeah. Okay? You know the c the oh my god. My phone's gonna Wait. Oh, shit. I haven't used scope. Codex before. Yeah. Okay. Okay. So, basically, basically, all you need to do is so Codex has a CLI. Right? Yeah. What you need to do is also, did you use Landgraf a lot? Just Jupyter Notebook, like, not in productions. Okay. That's fine. You you but you use Landgraf. You're familiar with it? Use Landgraf. Bro, like, how how how familiar are you with it? Yeah. I'm quite familiar. It is a great way to build a genetic system. Okay. Okay. So if you can get codec CLI working in a sandbox, let's say, E2B, So e to b sandbox. Codex and e to b. Clot code, n e to b. And then maybe CodeGen. CodeGen already works as an API. You don't need to do anything. Okay. All you need to do is pass in a prompt, and then it will give you the output. And it'll actually make a pull request for you or something. So see actually, it may not be possible with CodeGen. I don't know. So you have to look at that. Maybe for now, do cloud code. And codex. These two, And then make it work on SWE bench. For, like, 20 to 30 examples. Yeah. So then Let's see. What what does a, like, get? Right? And Yeah. See what result you get. Okay. And then if you can make a conclusion that clot code is better at codex, oh, that's golden. You get me? Okay. Then I can I can, like, literally go to Roland, be like, hey? This guy already did sweet Benchy bells. He's the perfect guy. Okay. And then and then it's an easy hire. Easy hire. Okay, Okay. Because because then nobody is gonna nobody's gonna do this take home yet. You're gonna be, like, the first one. I I really appreciate you believed in me. Let's try my best, bro. And Okay, bro. Oh, bro. Yes. Yes. Do you think I should come to the office to work, or I I should work from home? Come to the office. I like, I am there. You know? So you can come anytime. You can come But thanks for the clarifying the things. I didn't know what you guys doing. We were like, I didn't know that you guys are building site engineering, but I now got what you guys doing right now. Yes, bro. That's that's basically it. We are running cloud code in E2B. Oh, E2B for the sandbox. Yes. So Yes. So so if you can do that, and then after that, run you know, tasks from Suitebench. Okay. Perfect. Okay. So end goal is, like, you you're in the terminal, or you run a Python file, and then you can configure some stuff. Let's say, SWE bench dash dash clot code dash dash 10 examples. Right? Okay. Or bench dash dash codex does does 10 examples or 20 examples. And it runs and it runs, like, let's say, parallelly as well. You can have, like, five or 10 workers. In E2V. Actually, yeah. So you'll have a sandbox. But then you need to run this you need to run these emails somewhere. Maybe if you find a good place to run these emails, that's also great. You can either run it in a Docker container. Okay. Or you can run it in model functions. They should have harnesses for this already. So you can just look at Suibench and see what they're using. You can run them in, like, you can deploy them somewhere and run them run them somewhere, or you can use model. So Okay. Model, I think, is for the deployment of LLM application. Right? Yeah. But what you can so model has functions. Right? You can deploy a modal function. Okay. And then, basically, let's say you run five workers. Right? Yeah. This let's say let's say your function includes like, you extracting the input and then you're running the clot code in a sandbox Yeah. And then you're getting the output. That's inside the function. Right? Okay. You can run this model function, like, five times, 10 times per task. Do you get me? Yeah. Yeah. So you can basically, you have a sandbox, but you need to you need you need to figure out the best way to actually run these evals as well. You can either run them in the Docker container or you can run them in modal. Yeah. Just make it as easy as possible. Okay. Yeah. Sure. Are are you guys coming tomorrow? I will come tomorrow, of course. Okay. See you see you on at at What's your Can you give me a job? I can clean the clean offices. I get I get police officers very well. Bro, Okay. Okay, bro. See you tomorrow. A good night. I'm not joking. Bro. 