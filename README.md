# SWE-bench AI Evaluator

A comprehensive framework for evaluating and comparing AI coding tools (<PERSON>, <PERSON>, CodeGen) on SWE-bench tasks. This tool provides deterministic evaluation using existing test suites rather than LLM-as-judge approaches.

## Features

- **Multiple AI Evaluators**: Support for Claude, Codex CLI, and CodeGen API
- **Deterministic Testing**: Uses SWE-bench's existing test suites for objective evaluation
- **Sandboxed Execution**: Secure code execution using E2B or Docker containers
- **Parallel Processing**: Concurrent evaluation with configurable worker limits
- **Cost Tracking**: Built-in budget management and cost monitoring
- **Rich CLI**: Beautiful command-line interface with progress tracking
- **Modal Deployment**: Scalable cloud deployment option

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd swe-bench-evaluator

# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### 2. Configuration

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your API keys:

```env
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
E2B_API_KEY=your_e2b_api_key_here
```

### 3. Basic Usage

```bash
# Evaluate Claude on 10 tasks
python cli.py evaluate --model claude --examples 10

# Compare Claude and Codex on 20 tasks
python cli.py evaluate --model all --examples 20 --workers 3

# Get dataset information
python cli.py info --subset lite

# Check setup
python cli.py setup
```

## Detailed Usage

### Command Line Interface

#### Evaluate Models

```bash
# Single model evaluation
python cli.py evaluate --model claude --examples 20 --workers 5

# Compare multiple models
python cli.py evaluate --model all --examples 30 --workers 3 --budget 50.0

# Filter by repositories
python cli.py evaluate --model claude --examples 15 --repos "django/django,requests/requests"

# Use different dataset subset
python cli.py evaluate --model codex --examples 10 --subset verified
```

#### Options

- `--model`: AI model to evaluate (`claude`, `codex`, `codegen`, `all`)
- `--examples`: Number of examples to evaluate (default: 20)
- `--workers`: Number of concurrent workers (default: 5)
- `--subset`: SWE-bench dataset subset (`lite`, `verified`, `full`)
- `--repos`: Comma-separated list of repositories to filter by
- `--budget`: Maximum budget in USD (default: 100.0)

### Python API

```python
import asyncio
from src.evaluator import SWEBenchEvaluator
from src.models import EvaluatorType

async def main():
    evaluator = SWEBenchEvaluator()
    
    # Run comparison
    report = await evaluator.run_evaluation(
        subset="lite",
        max_tasks=20,
        evaluator_types=[EvaluatorType.CLAUDE, EvaluatorType.CODEX],
        max_concurrent=3
    )
    
    # Print results
    for evaluator_type, summary in report.summaries.items():
        print(f"{evaluator_type.value}: {summary.overall_success_rate:.1%} success rate")

asyncio.run(main())
```

### Modal Deployment

For large-scale evaluations, deploy to Modal:

```bash
# Install Modal
pip install modal

# Set up Modal secrets
modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key_here
modal secret create openai-api-key OPENAI_API_KEY=your_key_here
modal secret create e2b-api-key E2B_API_KEY=your_key_here

# Run evaluation on Modal
modal run modal_deployment.py --subset lite --max-tasks 50 --evaluators claude,codex --workers 5
```

## Architecture

### Core Components

1. **Evaluators** (`src/evaluators/`): AI tool integrations
   - `ClaudeEvaluator`: Anthropic Claude API integration
   - `CodexEvaluator`: OpenAI Codex CLI integration
   - `CodeGenEvaluator`: CodeGen API integration

2. **SWE-bench Integration** (`src/swe_bench/`):
   - `SWEBenchLoader`: Dataset loading and caching
   - `TestRunner`: Test execution in sandboxes
   - `ResultValidator`: Result validation and analysis

3. **Sandbox Management** (`src/sandbox/`):
   - `E2BSandboxManager`: E2B sandbox integration
   - `DockerSandboxManager`: Docker fallback option

4. **Utilities** (`src/utils/`):
   - Rate limiting and budget tracking
   - Structured logging
   - Configuration management

### Evaluation Process

1. **Task Loading**: Load SWE-bench tasks from official dataset
2. **Solution Generation**: Generate code solutions using AI evaluators
3. **Sandbox Setup**: Create isolated environment with repository
4. **Test Execution**: Run baseline tests, apply solution, run tests again
5. **Result Validation**: Validate results against SWE-bench criteria
6. **Analysis**: Generate comparison reports and metrics

### SWE-bench Evaluation Criteria

- **FAIL_TO_PASS tests**: Should fail before solution, pass after
- **PASS_TO_PASS tests**: Should pass both before and after solution
- **Deterministic**: Based on actual test execution, not LLM judgment

## Configuration

### Environment Variables

```env
# API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
E2B_API_KEY=your_e2b_api_key_here

# Evaluation Settings
MAX_BUDGET=100.0
DEFAULT_WORKERS=5
DEFAULT_EXAMPLES=20

# Paths
SWE_BENCH_DATASET_PATH=./data/swe_bench
RESULTS_OUTPUT_PATH=./results

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/evaluation.log
```

### Rate Limits

- Claude: 50 requests/minute
- OpenAI: 60 requests/minute
- Budget tracking with automatic stopping

## Results and Analysis

### Output Files

- `results/evaluation_results_*.json`: Raw evaluation results
- `results/evaluation_summary_*.json`: Aggregated summaries
- `results/comparison_report_*.json`: Multi-evaluator comparisons
- `logs/evaluation.log`: Detailed execution logs

### Metrics

- **Completion Rate**: Percentage of tasks completed successfully
- **Success Rate**: Percentage of tests passing (SWE-bench criteria)
- **Cost Efficiency**: Cost per successful test
- **Execution Time**: Average time per task

## Development

### Running Tests

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/

# Run with coverage
pytest --cov=src tests/
```

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all API keys are set in `.env` file
2. **E2B Connection Issues**: Check E2B API key and network connectivity
3. **Docker Issues**: Ensure Docker is installed and running
4. **Budget Exceeded**: Increase budget limit or reduce number of examples
5. **Codex CLI Not Found**: Install Codex CLI from OpenAI

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
python cli.py evaluate --model claude --examples 5
```

### Support

For issues and questions:
1. Check the logs in `logs/evaluation.log`
2. Review the troubleshooting section
3. Open an issue on GitHub

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Acknowledgments

- [SWE-bench](https://github.com/princeton-nlp/SWE-bench) for the evaluation dataset
- [E2B](https://e2b.dev/) for secure code execution sandboxes
- [Modal](https://modal.com/) for scalable cloud deployment
