"""Test coverage and missing test identification metrics."""

import ast
import re
from typing import Dict, Any, List, Set
from pathlib import Path


class TestCoverageMetrics:
    """Metrics for evaluating test coverage and missing test identification."""
    
    def __init__(self):
        self.common_test_patterns = [
            r'test_\w+',
            r'\w+_test',
            r'should_\w+'
        ]
    
    def evaluate_test_improvements(self, 
                                 vibe_code: str,
                                 production_code: str,
                                 vibe_tests: str = "",
                                 production_tests: str = "") -> Dict[str, Any]:
        """Evaluate test coverage improvements from vibe to production.
        
        Args:
            vibe_code: Original vibe-coded implementation
            production_code: Production-ready code
            vibe_tests: Original tests (if any)
            production_tests: Production-ready tests
            
        Returns:
            Dictionary of test coverage metrics
        """
        vibe_functions = self._extract_functions(vibe_code)
        prod_functions = self._extract_functions(production_code)
        
        vibe_test_coverage = self._calculate_test_coverage(vibe_code, vibe_tests)
        prod_test_coverage = self._calculate_test_coverage(production_code, production_tests)
        
        missing_tests = self._identify_missing_tests(production_code, production_tests)
        
        return {
            "vibe_coverage": vibe_test_coverage,
            "production_coverage": prod_test_coverage,
            "coverage_improvement": prod_test_coverage["coverage_percentage"] - vibe_test_coverage["coverage_percentage"],
            "missing_tests": missing_tests,
            "test_completeness_score": self._calculate_completeness_score(prod_test_coverage, missing_tests),
            "edge_case_coverage": self._analyze_edge_case_coverage(production_tests)
        }
    
    def _extract_functions(self, code: str) -> List[str]:
        """Extract function names from code."""
        try:
            tree = ast.parse(code)
        except SyntaxError:
            return []
        
        functions = []
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                functions.append(node.name)
        
        return functions
    
    def _calculate_test_coverage(self, code: str, tests: str) -> Dict[str, Any]:
        """Calculate test coverage for given code."""
        functions = self._extract_functions(code)
        test_functions = self._extract_test_functions(tests)
        
        if not functions:
            return {"coverage_percentage": 0.0, "covered_functions": [], "uncovered_functions": []}
        
        covered_functions = []
        for func in functions:
            if any(func.lower() in test.lower() for test in test_functions):
                covered_functions.append(func)
        
        uncovered_functions = [f for f in functions if f not in covered_functions]
        coverage_percentage = len(covered_functions) / len(functions) * 100
        
        return {
            "coverage_percentage": coverage_percentage,
            "covered_functions": covered_functions,
            "uncovered_functions": uncovered_functions,
            "total_functions": len(functions),
            "test_functions": test_functions
        }
    
    def _extract_test_functions(self, test_code: str) -> List[str]:
        """Extract test function names."""
        if not test_code:
            return []
        
        try:
            tree = ast.parse(test_code)
        except SyntaxError:
            return []
        
        test_functions = []
        for node in ast.walk(tree):
            if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                if any(re.match(pattern, node.name) for pattern in self.common_test_patterns):
                    test_functions.append(node.name)
        
        return test_functions
    
    def _identify_missing_tests(self, code: str, tests: str) -> List[Dict[str, Any]]:
        """Identify missing tests that should be written."""
        functions = self._extract_functions(code)
        test_coverage = self._calculate_test_coverage(code, tests)
        uncovered_functions = test_coverage["uncovered_functions"]
        
        missing_tests = []
        
        for func_name in uncovered_functions:
            missing_test_types = self._suggest_test_types(code, func_name)
            missing_tests.append({
                "function": func_name,
                "suggested_tests": missing_test_types,
                "priority": self._calculate_test_priority(code, func_name)
            })
        
        return missing_tests
    
    def _suggest_test_types(self, code: str, func_name: str) -> List[str]:
        """Suggest types of tests needed for a function."""
        suggested_tests = []
        
        try:
            tree = ast.parse(code)
        except SyntaxError:
            return ["test_basic_functionality"]
        
        function_node = None
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == func_name:
                function_node = node
                break
        
        if not function_node:
            return ["test_basic_functionality"]
        
        # Analyze function to suggest test types
        suggested_tests.append(f"test_{func_name}_basic")
        
        # Check for error handling
        has_exceptions = any(isinstance(node, ast.Raise) for node in ast.walk(function_node))
        if has_exceptions:
            suggested_tests.append(f"test_{func_name}_error_handling")
        
        # Check for conditionals (edge cases)
        has_conditionals = any(isinstance(node, ast.If) for node in ast.walk(function_node))
        if has_conditionals:
            suggested_tests.append(f"test_{func_name}_edge_cases")
        
        # Check for loops (boundary conditions)
        has_loops = any(isinstance(node, (ast.For, ast.While)) for node in ast.walk(function_node))
        if has_loops:
            suggested_tests.append(f"test_{func_name}_boundary_conditions")
        
        # Check for async functions
        if isinstance(function_node, ast.AsyncFunctionDef):
            suggested_tests.append(f"test_{func_name}_async_behavior")
        
        return suggested_tests
    
    def _calculate_test_priority(self, code: str, func_name: str) -> str:
        """Calculate priority for testing a function."""
        try:
            tree = ast.parse(code)
        except SyntaxError:
            return "medium"
        
        function_node = None
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) and node.name == func_name:
                function_node = node
                break
        
        if not function_node:
            return "medium"
        
        # High priority: public functions, error handling, complex logic
        if (not func_name.startswith('_') and 
            (any(isinstance(node, ast.Raise) for node in ast.walk(function_node)) or
             len(list(ast.walk(function_node))) > 20)):
            return "high"
        
        # Low priority: private functions, simple getters/setters
        if func_name.startswith('_') or len(list(ast.walk(function_node))) < 5:
            return "low"
        
        return "medium"
    
    def _calculate_completeness_score(self, coverage_data: Dict[str, Any], missing_tests: List[Dict[str, Any]]) -> float:
        """Calculate test completeness score."""
        base_coverage = coverage_data["coverage_percentage"] / 100
        
        # Penalty for missing high-priority tests
        high_priority_missing = sum(1 for test in missing_tests if test["priority"] == "high")
        penalty = high_priority_missing * 0.2
        
        return max(0.0, base_coverage - penalty)
    
    def _analyze_edge_case_coverage(self, test_code: str) -> Dict[str, Any]:
        """Analyze edge case coverage in tests."""
        if not test_code:
            return {"score": 0.0, "patterns": []}
        
        edge_case_patterns = [
            r'empty|null|none|zero',
            r'boundary|limit|max|min',
            r'invalid|error|exception',
            r'edge|corner|special'
        ]
        
        patterns_found = []
        for pattern in edge_case_patterns:
            if re.search(pattern, test_code, re.IGNORECASE):
                patterns_found.append(pattern)
        
        score = len(patterns_found) / len(edge_case_patterns)
        
        return {
            "score": score,
            "patterns": patterns_found,
            "coverage_types": self._categorize_test_coverage(test_code)
        }
    
    def _categorize_test_coverage(self, test_code: str) -> List[str]:
        """Categorize types of test coverage present."""
        categories = []
        
        coverage_indicators = {
            "unit": [r'test_\w+', r'assert'],
            "integration": [r'integration|end_to_end|e2e'],
            "performance": [r'performance|benchmark|timing'],
            "security": [r'security|auth|permission'],
            "error_handling": [r'error|exception|fail']
        }
        
        for category, patterns in coverage_indicators.items():
            if any(re.search(pattern, test_code, re.IGNORECASE) for pattern in patterns):
                categories.append(category)
        
        return categories