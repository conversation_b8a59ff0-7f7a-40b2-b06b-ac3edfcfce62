"""Modularity and code organization metrics."""

import ast
import re
from typing import Dict, Any, List, Set
from pathlib import Path


class ModularityMetrics:
    """Metrics for evaluating code modularity and organization."""
    
    def __init__(self):
        self.component_patterns = {
            "react": [r'function\s+\w+Component', r'const\s+\w+\s*=.*=>', r'class\s+\w+\s+extends'],
            "python": [r'class\s+\w+', r'def\s+\w+']
        }
    
    def evaluate_modularity_improvements(self, 
                                       vibe_code: str,
                                       production_code: str,
                                       vibe_files: List[str] = None,
                                       production_files: List[str] = None) -> Dict[str, Any]:
        """Evaluate modularity improvements from vibe to production code.
        
        Args:
            vibe_code: Original vibe-coded implementation
            production_code: Production-ready code
            vibe_files: List of file paths in vibe implementation
            production_files: List of file paths in production implementation
            
        Returns:
            Dictionary of modularity metrics
        """
        vibe_modularity = self._analyze_modularity(vibe_code, vibe_files or [])
        prod_modularity = self._analyze_modularity(production_code, production_files or [])
        
        separation_analysis = self._analyze_separation_of_concerns(vibe_code, production_code)
        
        return {
            "vibe_modularity": vibe_modularity,
            "production_modularity": prod_modularity,
            "modularity_improvement": self._calculate_modularity_improvement(vibe_modularity, prod_modularity),
            "separation_analysis": separation_analysis,
            "component_extraction": self._analyze_component_extraction(vibe_code, production_code),
            "file_organization_score": self._calculate_file_organization_score(vibe_files or [], production_files or [])
        }
    
    def _analyze_modularity(self, code: str, files: List[str]) -> Dict[str, Any]:
        """Analyze modularity characteristics of code."""
        try:
            tree = ast.parse(code)
        except SyntaxError:
            return {"score": 0.0, "metrics": {}}
        
        metrics = {
            "functions_per_file": self._calculate_functions_per_file(tree),
            "classes_per_file": self._calculate_classes_per_file(tree),
            "lines_per_function": self._calculate_lines_per_function(tree),
            "dependency_count": self._count_dependencies(tree),
            "cohesion_score": self._calculate_cohesion(tree),
            "coupling_score": self._calculate_coupling(tree)
        }
        
        overall_score = self._calculate_overall_modularity_score(metrics)
        
        return {
            "score": overall_score,
            "metrics": metrics,
            "file_count": len(files),
            "organization_pattern": self._detect_organization_pattern(files)
        }
    
    def _calculate_functions_per_file(self, tree: ast.AST) -> float:
        """Calculate average functions per file."""
        functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        return len(functions)
    
    def _calculate_classes_per_file(self, tree: ast.AST) -> float:
        """Calculate average classes per file."""
        classes = [node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
        return len(classes)
    
    def _calculate_lines_per_function(self, tree: ast.AST) -> float:
        """Calculate average lines per function."""
        functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        
        if not functions:
            return 0.0
        
        total_lines = sum(node.end_lineno - node.lineno + 1 for node in functions)
        return total_lines / len(functions)
    
    def _count_dependencies(self, tree: ast.AST) -> int:
        """Count import dependencies."""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                imports.extend(alias.name for alias in node.names)
            elif isinstance(node, ast.ImportFrom):
                imports.append(node.module or "")
        
        return len(set(imports))
    
    def _calculate_cohesion(self, tree: ast.AST) -> float:
        """Calculate cohesion score (how related functions are within a module)."""
        functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        
        if len(functions) < 2:
            return 1.0
        
        # Simple heuristic: functions that share variable names are more cohesive
        function_vars = {}
        for func in functions:
            vars_used = set()
            for node in ast.walk(func):
                if isinstance(node, ast.Name):
                    vars_used.add(node.id)
            function_vars[func.name] = vars_used
        
        # Calculate overlap between functions
        total_pairs = len(functions) * (len(functions) - 1) / 2
        if total_pairs == 0:
            return 1.0
        
        shared_pairs = 0
        func_names = list(function_vars.keys())
        for i in range(len(func_names)):
            for j in range(i + 1, len(func_names)):
                vars1 = function_vars[func_names[i]]
                vars2 = function_vars[func_names[j]]
                if vars1 & vars2:  # If they share variables
                    shared_pairs += 1
        
        return shared_pairs / total_pairs
    
    def _calculate_coupling(self, tree: ast.AST) -> float:
        """Calculate coupling score (lower is better)."""
        # Count external dependencies and cross-module calls
        external_calls = 0
        total_calls = 0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Call):
                total_calls += 1
                if isinstance(node.func, ast.Attribute):
                    # External method call
                    external_calls += 1
        
        if total_calls == 0:
            return 0.0
        
        return external_calls / total_calls
    
    def _calculate_overall_modularity_score(self, metrics: Dict[str, float]) -> float:
        """Calculate overall modularity score from individual metrics."""
        # Scoring criteria (normalized to 0-1)
        function_score = min(1.0, max(0.0, 1.0 - (metrics["functions_per_file"] - 10) / 20))  # Optimal: ~10 functions
        class_score = min(1.0, max(0.0, 1.0 - (metrics["classes_per_file"] - 3) / 7))  # Optimal: ~3 classes
        length_score = min(1.0, max(0.0, 1.0 - (metrics["lines_per_function"] - 15) / 30))  # Optimal: ~15 lines
        cohesion_score = metrics["cohesion_score"]
        coupling_score = 1.0 - metrics["coupling_score"]  # Lower coupling is better
        
        return (function_score + class_score + length_score + cohesion_score + coupling_score) / 5
    
    def _analyze_separation_of_concerns(self, vibe_code: str, production_code: str) -> Dict[str, Any]:
        """Analyze separation of concerns improvements."""
        vibe_concerns = self._identify_concerns(vibe_code)
        prod_concerns = self._identify_concerns(production_code)
        
        return {
            "vibe_concerns_mixed": len(vibe_concerns["mixed_concerns"]),
            "production_concerns_separated": len(prod_concerns["separated_concerns"]),
            "improvement_ratio": self._calculate_separation_improvement(vibe_concerns, prod_concerns),
            "concern_types": prod_concerns["concern_types"]
        }
    
    def _identify_concerns(self, code: str) -> Dict[str, Any]:
        """Identify different concerns in code."""
        concerns = {
            "ui": self._has_ui_code(code),
            "business_logic": self._has_business_logic(code),
            "data_access": self._has_data_access(code),
            "validation": self._has_validation(code),
            "error_handling": self._has_error_handling(code)
        }
        
        active_concerns = [k for k, v in concerns.items() if v]
        
        # If multiple concerns in single file/function, it's mixed
        mixed_concerns = active_concerns if len(active_concerns) > 2 else []
        separated_concerns = active_concerns if len(active_concerns) <= 2 else []
        
        return {
            "mixed_concerns": mixed_concerns,
            "separated_concerns": separated_concerns,
            "concern_types": active_concerns
        }
    
    def _has_ui_code(self, code: str) -> bool:
        """Check if code contains UI-related logic."""
        ui_patterns = [r'render|jsx|html|css|style|component', r'useState|useEffect|props']
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in ui_patterns)
    
    def _has_business_logic(self, code: str) -> bool:
        """Check if code contains business logic."""
        business_patterns = [r'calculate|process|validate|transform|logic']
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in business_patterns)
    
    def _has_data_access(self, code: str) -> bool:
        """Check if code contains data access logic."""
        data_patterns = [r'fetch|axios|api|database|sql|query|model']
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in data_patterns)
    
    def _has_validation(self, code: str) -> bool:
        """Check if code contains validation logic."""
        validation_patterns = [r'validate|check|verify|assert|required']
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in validation_patterns)
    
    def _has_error_handling(self, code: str) -> bool:
        """Check if code contains error handling."""
        error_patterns = [r'try|catch|error|exception|throw']
        return any(re.search(pattern, code, re.IGNORECASE) for pattern in error_patterns)
    
    def _calculate_separation_improvement(self, vibe_concerns: Dict, prod_concerns: Dict) -> float:
        """Calculate improvement in separation of concerns."""
        vibe_mixed = len(vibe_concerns["mixed_concerns"])
        prod_separated = len(prod_concerns["separated_concerns"])
        
        if vibe_mixed == 0:
            return 1.0 if prod_separated > 0 else 0.0
        
        return max(0.0, 1.0 - (vibe_mixed - prod_separated) / vibe_mixed)
    
    def _analyze_component_extraction(self, vibe_code: str, production_code: str) -> Dict[str, Any]:
        """Analyze component extraction improvements."""
        vibe_components = self._count_components(vibe_code)
        prod_components = self._count_components(production_code)
        
        return {
            "vibe_components": vibe_components,
            "production_components": prod_components,
            "components_extracted": max(0, prod_components - vibe_components),
            "extraction_quality": self._assess_extraction_quality(vibe_code, production_code)
        }
    
    def _count_components(self, code: str) -> int:
        """Count distinct components in code."""
        # Look for function components, class components, etc.
        component_patterns = [
            r'function\s+\w+Component',
            r'const\s+\w+\s*=.*=>',
            r'class\s+\w+\s+extends'
        ]
        
        components = 0
        for pattern in component_patterns:
            components += len(re.findall(pattern, code))
        
        return components
    
    def _assess_extraction_quality(self, vibe_code: str, production_code: str) -> float:
        """Assess quality of component extraction."""
        # Simple heuristic: if production code is more modular, extraction is good
        vibe_lines = len(vibe_code.split('\n'))
        prod_lines = len(production_code.split('\n'))
        
        if vibe_lines == 0:
            return 0.0
        
        # Good extraction should maintain or slightly increase total lines
        # but distribute them better
        line_ratio = prod_lines / vibe_lines
        
        if 0.8 <= line_ratio <= 1.3:  # Reasonable range
            return 1.0
        elif line_ratio < 0.8:  # Too much reduction (might be missing functionality)
            return 0.5
        else:  # Too much increase (might be over-engineering)
            return 0.7
    
    def _detect_organization_pattern(self, files: List[str]) -> str:
        """Detect organization pattern from file structure."""
        if not files:
            return "unknown"
        
        patterns = {
            "feature_based": any("feature" in f or "module" in f for f in files),
            "layer_based": any(layer in f for f in files for layer in ["service", "controller", "model", "view"]),
            "component_based": any("component" in f for f in files),
            "flat": len([f for f in files if "/" not in f]) > len(files) * 0.7
        }
        
        for pattern, matches in patterns.items():
            if matches:
                return pattern
        
        return "mixed"
    
    def _calculate_file_organization_score(self, vibe_files: List[str], production_files: List[str]) -> float:
        """Calculate file organization improvement score."""
        if not vibe_files and not production_files:
            return 0.0
        
        vibe_organization = self._assess_file_organization(vibe_files)
        prod_organization = self._assess_file_organization(production_files)
        
        return max(0.0, prod_organization - vibe_organization)
    
    def _assess_file_organization(self, files: List[str]) -> float:
        """Assess quality of file organization."""
        if not files:
            return 0.0
        
        score = 0.0
        
        # Check for proper directory structure
        has_directories = any("/" in f for f in files)
        if has_directories:
            score += 0.3
        
        # Check for separation by type/function
        file_types = set(f.split('.')[-1] for f in files if '.' in f)
        if len(file_types) > 1:
            score += 0.2
        
        # Check for consistent naming
        has_consistent_naming = self._check_naming_consistency(files)
        if has_consistent_naming:
            score += 0.3
        
        # Check for reasonable file count per directory
        dir_counts = {}
        for f in files:
            dir_name = "/".join(f.split("/")[:-1]) if "/" in f else "root"
            dir_counts[dir_name] = dir_counts.get(dir_name, 0) + 1
        
        avg_files_per_dir = sum(dir_counts.values()) / len(dir_counts)
        if 3 <= avg_files_per_dir <= 10:  # Reasonable range
            score += 0.2
        
        return min(1.0, score)
    
    def _check_naming_consistency(self, files: List[str]) -> bool:
        """Check if file naming is consistent."""
        if len(files) < 2:
            return True
        
        # Check for consistent case (snake_case, camelCase, etc.)
        naming_styles = {
            "snake_case": sum(1 for f in files if re.match(r'^[a-z_]+\.', f.split('/')[-1])),
            "camelCase": sum(1 for f in files if re.match(r'^[a-z][a-zA-Z]*\.', f.split('/')[-1])),
            "PascalCase": sum(1 for f in files if re.match(r'^[A-Z][a-zA-Z]*\.', f.split('/')[-1]))
        }
        
        dominant_style = max(naming_styles.values())
        return dominant_style / len(files) >= 0.8