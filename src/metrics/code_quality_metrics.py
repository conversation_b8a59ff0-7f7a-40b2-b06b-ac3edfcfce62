"""Code quality metrics for evaluating vibe-coded transformations."""

import ast
import re
from typing import Dict, Any, List
from pathlib import Path


class CodeQualityMetrics:
    """Metrics for evaluating code quality improvements."""
    
    def __init__(self):
        self.metrics = {
            "cyclomatic_complexity": 0,
            "code_duplication": 0,
            "function_length": 0,
            "import_organization": 0,
            "naming_conventions": 0,
            "error_handling": 0
        }
    
    def evaluate_code_quality(self, 
                            vibe_code: str, 
                            production_code: str) -> Dict[str, Any]:
        """Evaluate code quality transformation from vibe to production.
        
        Args:
            vibe_code: Original vibe-coded implementation
            production_code: Production-ready transformed code
            
        Returns:
            Dictionary of quality metrics and improvements
        """
        vibe_metrics = self._analyze_code(vibe_code)
        prod_metrics = self._analyze_code(production_code)
        
        improvements = {}
        for metric, vibe_value in vibe_metrics.items():
            prod_value = prod_metrics[metric]
            improvement = self._calculate_improvement(metric, vibe_value, prod_value)
            improvements[f"{metric}_improvement"] = improvement
        
        return {
            "vibe_metrics": vibe_metrics,
            "production_metrics": prod_metrics,
            "improvements": improvements,
            "overall_quality_score": self._calculate_overall_score(improvements)
        }
    
    def _analyze_code(self, code: str) -> Dict[str, float]:
        """Analyze code and extract quality metrics."""
        try:
            tree = ast.parse(code)
        except SyntaxError:
            return {k: 0.0 for k in self.metrics}
        
        return {
            "cyclomatic_complexity": self._calculate_complexity(tree),
            "code_duplication": self._detect_duplication(code),
            "function_length": self._average_function_length(tree),
            "import_organization": self._check_import_organization(tree),
            "naming_conventions": self._check_naming_conventions(tree),
            "error_handling": self._check_error_handling(tree)
        }
    
    def _calculate_complexity(self, tree: ast.AST) -> float:
        """Calculate cyclomatic complexity."""
        complexity = 0
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        return complexity
    
    def _detect_duplication(self, code: str) -> float:
        """Detect code duplication percentage."""
        lines = code.split('\n')
        unique_lines = set(line.strip() for line in lines if line.strip())
        if not lines:
            return 0.0
        return 1.0 - (len(unique_lines) / len(lines))
    
    def _average_function_length(self, tree: ast.AST) -> float:
        """Calculate average function length."""
        functions = [node for node in ast.walk(tree) 
                    if isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef))]
        
        if not functions:
            return 0.0
        
        total_lines = sum(node.end_lineno - node.lineno + 1 for node in functions)
        return total_lines / len(functions)
    
    def _check_import_organization(self, tree: ast.AST) -> float:
        """Check if imports are properly organized."""
        imports = []
        for node in ast.walk(tree):
            if isinstance(node, (ast.Import, ast.ImportFrom)):
                imports.append(node.lineno)
        
        if not imports:
            return 1.0
        
        # Check if imports are at the top and grouped
        imports_sorted = sorted(imports)
        if imports == imports_sorted and max(imports) < 20:
            return 1.0
        return 0.5
    
    def _check_naming_conventions(self, tree: ast.AST) -> float:
        """Check naming conventions (snake_case for functions, etc.)."""
        score = 1.0
        
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name):
                    score -= 0.1
            elif isinstance(node, ast.ClassDef):
                if not re.match(r'^[A-Z][a-zA-Z0-9]*$', node.name):
                    score -= 0.1
        
        return max(0.0, score)
    
    def _check_error_handling(self, tree: ast.AST) -> float:
        """Check presence of error handling."""
        try_blocks = sum(1 for node in ast.walk(tree) if isinstance(node, ast.Try))
        functions = sum(1 for node in ast.walk(tree) if isinstance(node, ast.FunctionDef))
        
        if functions == 0:
            return 0.0
        
        return min(1.0, try_blocks / functions)
    
    def _calculate_improvement(self, metric: str, vibe_value: float, prod_value: float) -> float:
        """Calculate improvement percentage for a metric."""
        if metric in ["cyclomatic_complexity", "code_duplication", "function_length"]:
            # Lower is better
            if vibe_value == 0:
                return 0.0
            return max(0, (vibe_value - prod_value) / vibe_value)
        else:
            # Higher is better
            if prod_value == 0:
                return 0.0
            return max(0, (prod_value - vibe_value) / max(prod_value, 1.0))
    
    def _calculate_overall_score(self, improvements: Dict[str, float]) -> float:
        """Calculate overall quality improvement score."""
        if not improvements:
            return 0.0
        return sum(improvements.values()) / len(improvements)