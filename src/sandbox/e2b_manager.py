"""E2B sandbox management for secure code execution."""

import asyncio
from typing import Dict, List, Optional, Any
from e2b import Sandbox
import tempfile
import os
from pathlib import Path

from ..models import SWEBenchTask, CodeSolution, TestResult
from ..config import config
from ..utils import logger


class E2BSandboxManager:
    """Manager for E2B sandboxes."""
    
    def __init__(self):
        self.api_key = config.e2b_api_key
        self.active_sandboxes: Dict[str, Sandbox] = {}
        
    async def create_sandbox(self, task_id: str, template: str = "python") -> str:
        """Create a new E2B sandbox for a task.
        
        Args:
            task_id: Unique identifier for the task
            template: Sandbox template to use
            
        Returns:
            Sandbox ID
        """
        try:
            sandbox = Sandbox(
                template=template,
                api_key=self.api_key,
                timeout=config.sandbox_timeout_seconds
            )
            
            self.active_sandboxes[task_id] = sandbox
            
            logger.info(f"Created E2B sandbox", 
                       task_id=task_id, 
                       sandbox_id=sandbox.id,
                       template=template)
            
            return sandbox.id
            
        except Exception as e:
            logger.error(f"Failed to create E2B sandbox", 
                        task_id=task_id, error=str(e))
            raise Exception(f"Failed to create sandbox: {str(e)}")
    
    async def setup_repository(self, task_id: str, task: SWEBenchTask) -> bool:
        """Set up the repository in the sandbox.
        
        Args:
            task_id: Task identifier
            task: SWE-bench task with repository information
            
        Returns:
            True if setup successful
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        
        try:
            # Clone the repository
            clone_cmd = f"git clone https://github.com/{task.repo}.git /repo"
            result = await self._run_command(sandbox, clone_cmd)
            
            if result.exit_code != 0:
                raise Exception(f"Failed to clone repository: {result.stderr}")
            
            # Checkout the base commit
            if task.base_commit:
                checkout_cmd = f"cd /repo && git checkout {task.base_commit}"
                result = await self._run_command(sandbox, checkout_cmd)
                
                if result.exit_code != 0:
                    logger.warning(f"Failed to checkout base commit", 
                                 task_id=task_id, 
                                 commit=task.base_commit,
                                 error=result.stderr)
            
            # Set up environment if specified
            if task.environment_setup_commit:
                setup_cmd = f"cd /repo && git checkout {task.environment_setup_commit}"
                await self._run_command(sandbox, setup_cmd)
            
            # Install dependencies (common patterns)
            await self._install_dependencies(sandbox)
            
            logger.info(f"Repository setup completed", task_id=task_id)
            return True
            
        except Exception as e:
            logger.error(f"Repository setup failed", 
                        task_id=task_id, error=str(e))
            return False
    
    async def apply_solution(self, task_id: str, solution: CodeSolution) -> bool:
        """Apply the generated solution to the repository.
        
        Args:
            task_id: Task identifier
            solution: Generated code solution
            
        Returns:
            True if solution applied successfully
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        
        try:
            # If specific files are mentioned, update them
            if solution.files_modified:
                for file_path in solution.files_modified:
                    await self._update_file(sandbox, file_path, solution.code)
            else:
                # Try to determine files to modify from the code
                await self._apply_code_intelligently(sandbox, solution.code)
            
            logger.info(f"Solution applied", 
                       task_id=task_id, 
                       files_modified=solution.files_modified)
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply solution", 
                        task_id=task_id, error=str(e))
            return False
    
    async def run_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests in the sandbox.
        
        Args:
            task_id: Task identifier
            task: SWE-bench task with test information
            
        Returns:
            List of test results
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        test_results = []
        
        try:
            # Run FAIL_TO_PASS tests (these should pass after our solution)
            for test_name in task.FAIL_TO_PASS:
                result = await self._run_single_test(sandbox, test_name)
                test_results.append(result)
            
            # Run PASS_TO_PASS tests (these should continue to pass)
            for test_name in task.PASS_TO_PASS:
                result = await self._run_single_test(sandbox, test_name)
                test_results.append(result)
            
            logger.info(f"Tests completed", 
                       task_id=task_id, 
                       total_tests=len(test_results))
            
            return test_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
    
    async def _run_single_test(self, sandbox: Sandbox, test_name: str) -> TestResult:
        """Run a single test and return the result."""
        
        # Common test runners
        test_commands = [
            f"cd /repo && python -m pytest {test_name} -v",
            f"cd /repo && python -m unittest {test_name}",
            f"cd /repo && python {test_name}",
            f"cd /repo && pytest {test_name}"
        ]
        
        for cmd in test_commands:
            try:
                result = await self._run_command(sandbox, cmd)
                
                # Determine test status
                status = "PASSED" if result.exit_code == 0 else "FAILED"
                
                return TestResult(
                    test_name=test_name,
                    status=status,
                    output=result.stdout + result.stderr,
                    execution_time=0.0,  # E2B doesn't provide timing
                    error_message=result.stderr if result.exit_code != 0 else None
                )
                
            except Exception as e:
                continue
        
        # If all test commands failed
        return TestResult(
            test_name=test_name,
            status="ERROR",
            output="",
            execution_time=0.0,
            error_message="Could not run test with any known test runner"
        )
    
    async def _run_command(self, sandbox: Sandbox, command: str, timeout: int = 60):
        """Run a command in the sandbox."""
        try:
            result = sandbox.process.start_and_wait(
                cmd=command,
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"Command execution failed", 
                        command=command, error=str(e))
            raise
    
    async def _install_dependencies(self, sandbox: Sandbox):
        """Install common dependencies in the repository."""
        
        dependency_commands = [
            "cd /repo && [ -f requirements.txt ] && pip install -r requirements.txt",
            "cd /repo && [ -f setup.py ] && pip install -e .",
            "cd /repo && [ -f pyproject.toml ] && pip install -e .",
            "cd /repo && [ -f package.json ] && npm install",
            "cd /repo && [ -f Pipfile ] && pipenv install"
        ]
        
        for cmd in dependency_commands:
            try:
                await self._run_command(sandbox, cmd)
            except:
                continue  # Ignore failures, not all repos will have these files
    
    async def _update_file(self, sandbox: Sandbox, file_path: str, content: str):
        """Update a specific file with new content."""
        
        # Create a temporary file with the content
        temp_file = f"/tmp/update_{hash(file_path)}.py"
        
        # Write content to temp file
        write_cmd = f"cat > {temp_file} << 'EOF'\n{content}\nEOF"
        await self._run_command(sandbox, write_cmd)
        
        # Copy to target location
        copy_cmd = f"cp {temp_file} /repo/{file_path}"
        await self._run_command(sandbox, copy_cmd)
    
    async def _apply_code_intelligently(self, sandbox: Sandbox, code: str):
        """Try to intelligently apply code to the repository."""
        
        # This is a simplified approach - in practice, you'd want more
        # sophisticated code parsing and file detection
        
        # Look for Python files in the code
        if "def " in code or "class " in code:
            # Assume it's Python code, try to find appropriate file
            find_cmd = "find /repo -name '*.py' -type f | head -5"
            result = await self._run_command(sandbox, find_cmd)
            
            if result.exit_code == 0 and result.stdout:
                files = result.stdout.strip().split('\n')
                if files:
                    # Apply to the first Python file found
                    await self._update_file(sandbox, files[0].replace('/repo/', ''), code)
    
    async def cleanup_sandbox(self, task_id: str):
        """Clean up and close the sandbox."""
        if task_id in self.active_sandboxes:
            try:
                sandbox = self.active_sandboxes[task_id]
                sandbox.close()
                del self.active_sandboxes[task_id]
                
                logger.info(f"Cleaned up sandbox", task_id=task_id)
                
            except Exception as e:
                logger.error(f"Failed to cleanup sandbox", 
                           task_id=task_id, error=str(e))
    
    async def cleanup_all(self):
        """Clean up all active sandboxes."""
        for task_id in list(self.active_sandboxes.keys()):
            await self.cleanup_sandbox(task_id)
