"""Docker sandbox management as fallback for E2B."""

import asyncio
import docker
import tempfile
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..models import SWEBenchTask, CodeSolution, TestResult
from ..config import config
from ..utils import logger


class DockerSandboxManager:
    """Manager for Docker-based sandboxes."""
    
    def __init__(self):
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {str(e)}")
            raise Exception("Docker not available. Please install Docker or use E2B.")
        
        self.active_containers: Dict[str, docker.models.containers.Container] = {}
        self.temp_dirs: Dict[str, str] = {}
    
    async def create_sandbox(self, task_id: str, template: str = "python") -> str:
        """Create a new Docker container for a task.
        
        Args:
            task_id: Unique identifier for the task
            template: Container template to use
            
        Returns:
            Container ID
        """
        try:
            # Create temporary directory for this task
            temp_dir = tempfile.mkdtemp(prefix=f"swe_bench_{task_id}_")
            self.temp_dirs[task_id] = temp_dir
            
            # Choose appropriate Docker image
            image = self._get_docker_image(template)
            
            # Create and start container
            container = self.docker_client.containers.run(
                image,
                command="sleep infinity",  # Keep container running
                detach=True,
                volumes={temp_dir: {'bind': '/workspace', 'mode': 'rw'}},
                working_dir='/workspace',
                name=f"swe_bench_{task_id}",
                remove=True  # Auto-remove when stopped
            )
            
            self.active_containers[task_id] = container
            
            logger.info(f"Created Docker sandbox", 
                       task_id=task_id, 
                       container_id=container.id[:12],
                       image=image)
            
            return container.id
            
        except Exception as e:
            logger.error(f"Failed to create Docker sandbox", 
                        task_id=task_id, error=str(e))
            raise Exception(f"Failed to create Docker sandbox: {str(e)}")
    
    def _get_docker_image(self, template: str) -> str:
        """Get appropriate Docker image for template."""
        images = {
            "python": "python:3.9-slim",
            "node": "node:16-slim",
            "ubuntu": "ubuntu:20.04"
        }
        return images.get(template, "python:3.9-slim")
    
    async def setup_repository(self, task_id: str, task: SWEBenchTask) -> bool:
        """Set up the repository in the Docker container."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        
        try:
            # Install git if not present
            self._exec_command(container, "apt-get update && apt-get install -y git")
            
            # Clone the repository
            clone_cmd = f"git clone https://github.com/{task.repo}.git /workspace/repo"
            result = self._exec_command(container, clone_cmd)
            
            if result.exit_code != 0:
                raise Exception(f"Failed to clone repository: {result.output}")
            
            # Checkout the base commit
            if task.base_commit:
                checkout_cmd = f"cd /workspace/repo && git checkout {task.base_commit}"
                result = self._exec_command(container, checkout_cmd)
                
                if result.exit_code != 0:
                    logger.warning(f"Failed to checkout base commit", 
                                 task_id=task_id, 
                                 commit=task.base_commit,
                                 error=result.output)
            
            # Install Python dependencies
            self._install_python_dependencies(container)
            
            logger.info(f"Repository setup completed", task_id=task_id)
            return True
            
        except Exception as e:
            logger.error(f"Repository setup failed", 
                        task_id=task_id, error=str(e))
            return False
    
    def _install_python_dependencies(self, container):
        """Install Python dependencies in the container."""
        
        # Install pip if not present
        self._exec_command(container, "apt-get install -y python3-pip")
        
        # Common dependency installation commands
        dep_commands = [
            "cd /workspace/repo && [ -f requirements.txt ] && pip install -r requirements.txt || true",
            "cd /workspace/repo && [ -f setup.py ] && pip install -e . || true",
            "cd /workspace/repo && [ -f pyproject.toml ] && pip install -e . || true",
            "pip install pytest unittest2 nose"  # Common test frameworks
        ]
        
        for cmd in dep_commands:
            try:
                self._exec_command(container, cmd)
            except:
                continue  # Ignore failures
    
    async def apply_solution(self, task_id: str, solution: CodeSolution) -> bool:
        """Apply the generated solution to the repository."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        
        try:
            if solution.files_modified:
                for file_path in solution.files_modified:
                    self._update_file(container, file_path, solution.code)
            else:
                # Try to apply code intelligently
                self._apply_code_intelligently(container, solution.code)
            
            logger.info(f"Solution applied", 
                       task_id=task_id, 
                       files_modified=solution.files_modified)
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply solution", 
                        task_id=task_id, error=str(e))
            return False
    
    def _update_file(self, container, file_path: str, content: str):
        """Update a specific file with new content."""
        
        # Write content to a temporary file in the host temp directory
        task_id = self._get_task_id_for_container(container)
        temp_dir = self.temp_dirs[task_id]
        temp_file = os.path.join(temp_dir, "temp_update.py")
        
        with open(temp_file, 'w') as f:
            f.write(content)
        
        # Copy from temp location to target location in container
        copy_cmd = f"cp /workspace/temp_update.py /workspace/repo/{file_path}"
        self._exec_command(container, copy_cmd)
    
    def _get_task_id_for_container(self, container) -> str:
        """Get task ID for a container."""
        for task_id, cont in self.active_containers.items():
            if cont.id == container.id:
                return task_id
        raise Exception("Container not found in active containers")
    
    def _apply_code_intelligently(self, container, code: str):
        """Try to intelligently apply code to the repository."""
        
        # Look for Python files in the repository
        if "def " in code or "class " in code:
            find_cmd = "find /workspace/repo -name '*.py' -type f | head -5"
            result = self._exec_command(container, find_cmd)
            
            if result.exit_code == 0 and result.output:
                files = result.output.strip().split('\n')
                if files:
                    # Apply to the first Python file found
                    file_path = files[0].replace('/workspace/repo/', '')
                    self._update_file(container, file_path, code)
    
    async def run_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests in the Docker container."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        test_results = []
        
        try:
            # Run FAIL_TO_PASS tests
            for test_name in task.FAIL_TO_PASS:
                result = self._run_single_test(container, test_name)
                test_results.append(result)
            
            # Run PASS_TO_PASS tests
            for test_name in task.PASS_TO_PASS:
                result = self._run_single_test(container, test_name)
                test_results.append(result)
            
            logger.info(f"Tests completed", 
                       task_id=task_id, 
                       total_tests=len(test_results))
            
            return test_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
    
    def _run_single_test(self, container, test_name: str) -> TestResult:
        """Run a single test and return the result."""
        
        # Common test runners
        test_commands = [
            f"cd /workspace/repo && python -m pytest {test_name} -v",
            f"cd /workspace/repo && python -m unittest {test_name}",
            f"cd /workspace/repo && python {test_name}",
            f"cd /workspace/repo && pytest {test_name}"
        ]
        
        for cmd in test_commands:
            try:
                result = self._exec_command(container, cmd)
                
                # Determine test status
                status = "PASSED" if result.exit_code == 0 else "FAILED"
                
                return TestResult(
                    test_name=test_name,
                    status=status,
                    output=result.output,
                    execution_time=0.0,  # Docker doesn't provide timing
                    error_message=result.output if result.exit_code != 0 else None
                )
                
            except Exception:
                continue
        
        # If all test commands failed
        return TestResult(
            test_name=test_name,
            status="ERROR",
            output="",
            execution_time=0.0,
            error_message="Could not run test with any known test runner"
        )
    
    def _exec_command(self, container, command: str):
        """Execute a command in the container."""
        try:
            result = container.exec_run(command, workdir="/workspace")
            
            # Create a simple result object
            class ExecResult:
                def __init__(self, exit_code, output):
                    self.exit_code = exit_code
                    self.output = output.decode() if isinstance(output, bytes) else output
                    self.stdout = self.output
                    self.stderr = ""
            
            return ExecResult(result.exit_code, result.output)
            
        except Exception as e:
            logger.error(f"Command execution failed", 
                        command=command, error=str(e))
            raise
    
    async def cleanup_sandbox(self, task_id: str):
        """Clean up and remove the Docker container."""
        if task_id in self.active_containers:
            try:
                container = self.active_containers[task_id]
                container.stop()
                container.remove()
                del self.active_containers[task_id]
                
                logger.info(f"Cleaned up Docker container", task_id=task_id)
                
            except Exception as e:
                logger.error(f"Failed to cleanup container", 
                           task_id=task_id, error=str(e))
        
        # Clean up temporary directory
        if task_id in self.temp_dirs:
            try:
                shutil.rmtree(self.temp_dirs[task_id])
                del self.temp_dirs[task_id]
            except Exception as e:
                logger.error(f"Failed to cleanup temp directory", 
                           task_id=task_id, error=str(e))
    
    async def cleanup_all(self):
        """Clean up all active containers."""
        for task_id in list(self.active_containers.keys()):
            await self.cleanup_sandbox(task_id)
