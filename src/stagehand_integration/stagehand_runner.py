"""Stagehand integration for automated UI testing."""

import asyncio
import json
import subprocess
from typing import Dict, Any, List, Optional
from pathlib import Path
import tempfile
import base64

from ..utils import logger
from ..config import config


class StagehandRunner:
    """Runner for Stagehand browser automation and UI testing."""
    
    def __init__(self):
        self.temp_dir = Path(tempfile.mkdtemp(prefix="stagehand_"))
        self.screenshots_dir = self.temp_dir / "screenshots"
        self.screenshots_dir.mkdir(exist_ok=True)
        
    async def validate_ui_functionality(self, 
                                      app_url: str,
                                      test_scenarios: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Validate UI functionality using Stagehand automation.
        
        Args:
            app_url: URL of the application to test
            test_scenarios: List of test scenarios to run
            
        Returns:
            Validation results with screenshots and interaction outcomes
        """
        logger.info(f"Starting UI validation for {app_url}")
        
        results = {
            "app_url": app_url,
            "total_scenarios": len(test_scenarios),
            "passed_scenarios": 0,
            "failed_scenarios": 0,
            "scenario_results": [],
            "screenshots": []
        }
        
        for i, scenario in enumerate(test_scenarios):
            try:
                scenario_result = await self._run_scenario(app_url, scenario, i)
                results["scenario_results"].append(scenario_result)
                
                if scenario_result["status"] == "passed":
                    results["passed_scenarios"] += 1
                else:
                    results["failed_scenarios"] += 1
                    
            except Exception as e:
                logger.error(f"Scenario {i} failed: {str(e)}")
                results["scenario_results"].append({
                    "scenario_id": i,
                    "name": scenario.get("name", f"Scenario {i}"),
                    "status": "error",
                    "error": str(e)
                })
                results["failed_scenarios"] += 1
        
        results["success_rate"] = results["passed_scenarios"] / max(1, results["total_scenarios"])
        
        logger.info(f"UI validation completed: {results['passed_scenarios']}/{results['total_scenarios']} scenarios passed")
        return results
    
    async def _run_scenario(self, app_url: str, scenario: Dict[str, Any], scenario_id: int) -> Dict[str, Any]:
        """Run a single test scenario."""
        
        scenario_name = scenario.get("name", f"Scenario {scenario_id}")
        logger.info(f"Running scenario: {scenario_name}")
        
        # Create Stagehand script
        script_content = self._generate_stagehand_script(app_url, scenario)
        script_file = self.temp_dir / f"scenario_{scenario_id}.js"
        
        with open(script_file, 'w') as f:
            f.write(script_content)
        
        # Run Stagehand script
        try:
            result = await self._execute_stagehand_script(script_file, scenario_id)
            
            return {
                "scenario_id": scenario_id,
                "name": scenario_name,
                "status": result["status"],
                "execution_time": result["execution_time"],
                "screenshots": result["screenshots"],
                "interactions": result["interactions"],
                "assertions": result["assertions"],
                "error": result.get("error")
            }
            
        except Exception as e:
            return {
                "scenario_id": scenario_id,
                "name": scenario_name,
                "status": "error",
                "error": str(e)
            }
    
    def _generate_stagehand_script(self, app_url: str, scenario: Dict[str, Any]) -> str:
        """Generate Stagehand JavaScript script for a scenario."""
        
        script = f"""
const {{ Stagehand }} = require("@browserbasehq/stagehand");
const fs = require('fs');
const path = require('path');

async function runScenario() {{
    const stagehand = new Stagehand({{
        env: "LOCAL",
        debugDom: true,
        headless: false
    }});

    let results = {{
        status: "passed",
        execution_time: 0,
        screenshots: [],
        interactions: [],
        assertions: [],
        error: null
    }};

    const startTime = Date.now();

    try {{
        await stagehand.init();
        await stagehand.page.goto("{app_url}");
        
        // Take initial screenshot
        const initialScreenshot = await stagehand.page.screenshot({{ 
            path: path.join("{self.screenshots_dir}", "scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_initial.png"),
            fullPage: true 
        }});
        results.screenshots.push("scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_initial.png");
"""
        
        # Add scenario steps
        steps = scenario.get("steps", [])
        for i, step in enumerate(steps):
            script += self._generate_step_code(step, i)
        
        script += f"""
        
        // Take final screenshot
        const finalScreenshot = await stagehand.page.screenshot({{ 
            path: path.join("{self.screenshots_dir}", "scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_final.png"),
            fullPage: true 
        }});
        results.screenshots.push("scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_final.png");
        
        results.execution_time = Date.now() - startTime;
        
    }} catch (error) {{
        results.status = "failed";
        results.error = error.message;
        
        // Take error screenshot
        try {{
            await stagehand.page.screenshot({{ 
                path: path.join("{self.screenshots_dir}", "scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_error.png"),
                fullPage: true 
            }});
            results.screenshots.push("scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_error.png");
        }} catch (screenshotError) {{
            console.error("Failed to take error screenshot:", screenshotError);
        }}
    }} finally {{
        await stagehand.close();
    }}

    // Write results to file
    fs.writeFileSync(
        path.join("{self.temp_dir}", "scenario_{scenario.get('name', 'unnamed').replace(' ', '_')}_results.json"),
        JSON.stringify(results, null, 2)
    );

    return results;
}}

runScenario().then(results => {{
    console.log("Scenario completed:", JSON.stringify(results, null, 2));
    process.exit(results.status === "passed" ? 0 : 1);
}}).catch(error => {{
    console.error("Scenario failed:", error);
    process.exit(1);
}});
"""
        
        return script
    
    def _generate_step_code(self, step: Dict[str, Any], step_index: int) -> str:
        """Generate JavaScript code for a single step."""
        
        step_type = step.get("type", "unknown")
        step_code = f"\n        // Step {step_index + 1}: {step.get('description', step_type)}\n"
        
        if step_type == "click":
            selector = step.get("selector", step.get("element", ""))
            step_code += f"""        
        await stagehand.act("{{action: 'click', element: '{selector}'}}");
        results.interactions.push({{
            step: {step_index + 1},
            action: "click",
            element: "{selector}",
            timestamp: Date.now() - startTime
        }});
"""
        
        elif step_type == "type":
            selector = step.get("selector", step.get("element", ""))
            text = step.get("text", "")
            step_code += f"""
        await stagehand.act("{{action: 'type', element: '{selector}', text: '{text}'}}");
        results.interactions.push({{
            step: {step_index + 1},
            action: "type", 
            element: "{selector}",
            text: "{text}",
            timestamp: Date.now() - startTime
        }});
"""
        
        elif step_type == "wait":
            timeout = step.get("timeout", 1000)
            step_code += f"""
        await stagehand.page.waitForTimeout({timeout});
        results.interactions.push({{
            step: {step_index + 1},
            action: "wait",
            timeout: {timeout},
            timestamp: Date.now() - startTime
        }});
"""
        
        elif step_type == "assert_text":
            text = step.get("text", "")
            step_code += f"""
        const pageContent = await stagehand.page.content();
        const textFound = pageContent.includes("{text}");
        results.assertions.push({{
            step: {step_index + 1},
            type: "text_present",
            expected: "{text}",
            result: textFound,
            timestamp: Date.now() - startTime
        }});
        if (!textFound) {{
            throw new Error("Expected text '{text}' not found on page");
        }}
"""
        
        elif step_type == "assert_element":
            selector = step.get("selector", step.get("element", ""))
            step_code += f"""
        const element = await stagehand.page.$("{selector}");
        const elementExists = element !== null;
        results.assertions.push({{
            step: {step_index + 1},
            type: "element_exists",
            selector: "{selector}",
            result: elementExists,
            timestamp: Date.now() - startTime
        }});
        if (!elementExists) {{
            throw new Error("Expected element '{selector}' not found");
        }}
"""
        
        elif step_type == "screenshot":
            name = step.get("name", f"step_{step_index + 1}")
            step_code += f"""
        await stagehand.page.screenshot({{ 
            path: path.join("{self.screenshots_dir}", "{name}.png"),
            fullPage: true 
        }});
        results.screenshots.push("{name}.png");
        results.interactions.push({{
            step: {step_index + 1},
            action: "screenshot",
            filename: "{name}.png",
            timestamp: Date.now() - startTime
        }});
"""
        
        return step_code
    
    async def _execute_stagehand_script(self, script_file: Path, scenario_id: int) -> Dict[str, Any]:
        """Execute a Stagehand script and return results."""
        
        try:
            # Run the Node.js script
            process = await asyncio.create_subprocess_exec(
                "node", str(script_file),
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(self.temp_dir)
            )
            
            stdout, stderr = await process.communicate()
            
            # Read results from file
            results_file = self.temp_dir / f"scenario_{scenario_id}_results.json"
            if results_file.exists():
                with open(results_file, 'r') as f:
                    results = json.load(f)
            else:
                results = {
                    "status": "error",
                    "error": "No results file generated",
                    "execution_time": 0,
                    "screenshots": [],
                    "interactions": [],
                    "assertions": []
                }
            
            # Add stdout/stderr to results
            if stdout:
                results["stdout"] = stdout.decode()
            if stderr:
                results["stderr"] = stderr.decode()
            
            return results
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "execution_time": 0,
                "screenshots": [],
                "interactions": [],
                "assertions": []
            }
    
    def create_test_scenarios_from_features(self, features: List[str]) -> List[Dict[str, Any]]:
        """Create test scenarios from feature descriptions.
        
        Args:
            features: List of feature descriptions
            
        Returns:
            List of test scenarios for Stagehand
        """
        scenarios = []
        
        for i, feature in enumerate(features):
            scenario = {
                "name": f"Test {feature}",
                "description": f"Automated test for {feature}",
                "steps": self._generate_steps_for_feature(feature)
            }
            scenarios.append(scenario)
        
        return scenarios
    
    def _generate_steps_for_feature(self, feature: str) -> List[Dict[str, Any]]:
        """Generate test steps for a feature."""
        
        # Simple heuristics for common features
        feature_lower = feature.lower()
        steps = []
        
        if "login" in feature_lower:
            steps = [
                {"type": "click", "element": "#login-button", "description": "Click login button"},
                {"type": "type", "element": "#username", "text": "testuser", "description": "Enter username"},
                {"type": "type", "element": "#password", "text": "testpass", "description": "Enter password"},
                {"type": "click", "element": "#submit", "description": "Submit login form"},
                {"type": "wait", "timeout": 2000},
                {"type": "assert_text", "text": "Dashboard", "description": "Verify login success"}
            ]
        
        elif "form" in feature_lower:
            steps = [
                {"type": "click", "element": "form input:first-child", "description": "Click first input"},
                {"type": "type", "element": "form input:first-child", "text": "test value", "description": "Enter test value"},
                {"type": "click", "element": "form button[type='submit']", "description": "Submit form"},
                {"type": "wait", "timeout": 2000},
                {"type": "assert_text", "text": "Success", "description": "Verify form submission"}
            ]
        
        elif "navigation" in feature_lower or "menu" in feature_lower:
            steps = [
                {"type": "click", "element": "nav a:first-child", "description": "Click first nav link"},
                {"type": "wait", "timeout": 1000},
                {"type": "screenshot", "name": "navigation_test", "description": "Take screenshot"},
                {"type": "assert_element", "element": "main", "description": "Verify main content area"}
            ]
        
        elif "button" in feature_lower:
            steps = [
                {"type": "click", "element": "button", "description": "Click button"},
                {"type": "wait", "timeout": 1000},
                {"type": "screenshot", "name": "button_test", "description": "Take screenshot after click"}
            ]
        
        else:
            # Generic steps for unknown features
            steps = [
                {"type": "screenshot", "name": f"feature_test_{feature.replace(' ', '_')}", "description": "Take initial screenshot"},
                {"type": "wait", "timeout": 1000},
                {"type": "assert_element", "element": "body", "description": "Verify page loaded"}
            ]
        
        return steps
    
    async def cleanup(self):
        """Clean up temporary files."""
        try:
            import shutil
            shutil.rmtree(self.temp_dir)
            logger.info("Cleaned up Stagehand temporary files")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp directory: {str(e)}")
    
    def get_screenshot_paths(self) -> List[str]:
        """Get paths to all captured screenshots."""
        screenshot_files = list(self.screenshots_dir.glob("*.png"))
        return [str(f) for f in screenshot_files]
    
    def encode_screenshots_base64(self) -> Dict[str, str]:
        """Encode all screenshots as base64 for easy transmission."""
        screenshots = {}
        
        for screenshot_path in self.get_screenshot_paths():
            try:
                with open(screenshot_path, 'rb') as f:
                    screenshot_data = base64.b64encode(f.read()).decode()
                    filename = Path(screenshot_path).name
                    screenshots[filename] = screenshot_data
            except Exception as e:
                logger.warning(f"Failed to encode screenshot {screenshot_path}: {str(e)}")
        
        return screenshots