"""Dataset generation for vibe-coded to production-ready transformations."""

import json
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
import hashlib

from ..models import SWEBenchTask, CodeSolution
from ..evaluators import Claude<PERSON><PERSON>uator
from ..config import config
from ..utils import logger


class DatasetGenerator:
    """Generate datasets of vibe-coded to production-ready transformations."""
    
    def __init__(self):
        self.output_dir = Path("data/collected_datasets")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    async def generate_vibe_production_pairs(self, 
                                           tasks: List[SWEBenchTask],
                                           num_samples: int = 100) -> List[Dict[str, Any]]:
        """Generate vibe-coded to production-ready pairs.
        
        Args:
            tasks: List of SWE-bench tasks to use as base
            num_samples: Number of samples to generate
            
        Returns:
            List of datasets with vibe/production pairs
        """
        logger.info(f"Generating {num_samples} vibe-production pairs")
        
        dataset = []
        evaluator = ClaudeEvaluator()
        
        for i, task in enumerate(tasks[:num_samples]):
            try:
                # Generate vibe-coded solution
                vibe_solution = await self._generate_vibe_solution(evaluator, task)
                
                # Generate production-ready solution
                production_solution = await self._generate_production_solution(evaluator, task, vibe_solution)
                
                # Create dataset entry
                entry = self._create_dataset_entry(task, vibe_solution, production_solution)
                dataset.append(entry)
                
                logger.info(f"Generated pair {i+1}/{num_samples}")
                
            except Exception as e:
                logger.error(f"Failed to generate pair for task {task.instance_id}: {str(e)}")
                continue
        
        # Save dataset
        dataset_path = self._save_dataset(dataset, "vibe_production_pairs")
        logger.info(f"Saved {len(dataset)} pairs to {dataset_path}")
        
        return dataset
    
    async def _generate_vibe_solution(self, evaluator: ClaudeEvaluator, task: SWEBenchTask) -> CodeSolution:
        """Generate a vibe-coded solution (quick and dirty)."""
        
        vibe_prompt = f"""
You are rapidly prototyping a solution. Write quick, working code that solves the problem.
Don't worry about best practices, modularity, or extensive testing.
Just make it work as fast as possible.

Problem: {task.problem_statement}
Hints: {task.hints_text}

Write a quick solution that works.
"""
        
        # Override the system prompt to encourage vibe coding
        original_prompt = evaluator._create_system_prompt
        evaluator._create_system_prompt = lambda: vibe_prompt
        
        try:
            solution = await evaluator.generate_solution(task)
            solution.explanation = "Vibe-coded solution - quick implementation"
            return solution
        finally:
            evaluator._create_system_prompt = original_prompt
    
    async def _generate_production_solution(self, 
                                          evaluator: ClaudeEvaluator, 
                                          task: SWEBenchTask,
                                          vibe_solution: CodeSolution) -> CodeSolution:
        """Generate production-ready solution based on vibe code."""
        
        production_prompt = f"""
You are refactoring vibe-coded solution into production-ready code.

Original Problem: {task.problem_statement}

Vibe Code (quick and dirty):
{vibe_solution.code}

Transform this into production-ready code with:
1. Proper modularity and separation of concerns
2. Comprehensive error handling
3. Clean, readable code structure
4. Proper documentation
5. Missing tests that should be written

Focus on making the code maintainable, testable, and following best practices.
"""
        
        # Create new task for production refactoring
        production_task = SWEBenchTask(
            instance_id=f"{task.instance_id}_production",
            repo=task.repo,
            base_commit=task.base_commit,
            problem_statement=production_prompt,
            hints_text="Transform vibe code to production-ready",
            created_at=task.created_at,
            version=task.version,
            FAIL_TO_PASS=task.FAIL_TO_PASS,
            PASS_TO_PASS=task.PASS_TO_PASS
        )
        
        solution = await evaluator.generate_solution(production_task)
        solution.explanation = "Production-ready refactored solution"
        return solution
    
    def _create_dataset_entry(self, 
                            task: SWEBenchTask,
                            vibe_solution: CodeSolution,
                            production_solution: CodeSolution) -> Dict[str, Any]:
        """Create a dataset entry from task and solutions."""
        
        entry_id = hashlib.md5(f"{task.instance_id}_{datetime.now().isoformat()}".encode()).hexdigest()
        
        return {
            "id": entry_id,
            "created_at": datetime.now().isoformat(),
            "source_task": {
                "instance_id": task.instance_id,
                "repo": task.repo,
                "problem_statement": task.problem_statement,
                "hints_text": task.hints_text
            },
            "input": {
                "type": "vibe_coded",
                "code": vibe_solution.code,
                "explanation": vibe_solution.explanation,
                "files_modified": vibe_solution.files_modified,
                "confidence_score": vibe_solution.confidence_score
            },
            "output": {
                "type": "production_ready",
                "code": production_solution.code,
                "explanation": production_solution.explanation,
                "files_modified": production_solution.files_modified,
                "confidence_score": production_solution.confidence_score
            },
            "transformation_type": "vibe_to_production",
            "quality_metrics": self._calculate_transformation_metrics(vibe_solution, production_solution)
        }
    
    def _calculate_transformation_metrics(self, 
                                        vibe_solution: CodeSolution,
                                        production_solution: CodeSolution) -> Dict[str, Any]:
        """Calculate basic metrics for the transformation."""
        
        vibe_lines = len(vibe_solution.code.split('\n'))
        prod_lines = len(production_solution.code.split('\n'))
        
        return {
            "line_count_change": prod_lines - vibe_lines,
            "line_count_ratio": prod_lines / max(1, vibe_lines),
            "files_added": len(production_solution.files_modified) - len(vibe_solution.files_modified),
            "explanation_length_change": len(production_solution.explanation) - len(vibe_solution.explanation),
            "confidence_change": (production_solution.confidence_score or 0) - (vibe_solution.confidence_score or 0)
        }
    
    def _save_dataset(self, dataset: List[Dict[str, Any]], name: str) -> str:
        """Save dataset to file."""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{name}_{timestamp}.json"
        filepath = self.output_dir / filename
        
        with open(filepath, 'w') as f:
            json.dump({
                "metadata": {
                    "created_at": datetime.now().isoformat(),
                    "dataset_name": name,
                    "num_samples": len(dataset),
                    "version": "1.0"
                },
                "data": dataset
            }, f, indent=2)
        
        return str(filepath)
    
    async def generate_synthetic_dataset(self, 
                                       base_features: List[str],
                                       num_samples: int = 50) -> List[Dict[str, Any]]:
        """Generate synthetic vibe-coded features and their production versions.
        
        Args:
            base_features: List of feature descriptions to generate
            num_samples: Number of samples per feature
            
        Returns:
            Synthetic dataset
        """
        logger.info(f"Generating synthetic dataset with {len(base_features)} features")
        
        dataset = []
        evaluator = ClaudeEvaluator()
        
        for feature_desc in base_features:
            for i in range(num_samples):
                try:
                    # Create synthetic task
                    synthetic_task = self._create_synthetic_task(feature_desc, i)
                    
                    # Generate vibe and production solutions
                    vibe_solution = await self._generate_vibe_solution(evaluator, synthetic_task)
                    production_solution = await self._generate_production_solution(evaluator, synthetic_task, vibe_solution)
                    
                    entry = self._create_dataset_entry(synthetic_task, vibe_solution, production_solution)
                    dataset.append(entry)
                    
                    logger.info(f"Generated synthetic sample {i+1}/{num_samples} for feature: {feature_desc}")
                    
                except Exception as e:
                    logger.error(f"Failed to generate synthetic sample: {str(e)}")
                    continue
        
        # Save synthetic dataset
        dataset_path = self._save_dataset(dataset, "synthetic_vibe_production")
        logger.info(f"Saved {len(dataset)} synthetic samples to {dataset_path}")
        
        return dataset
    
    def _create_synthetic_task(self, feature_desc: str, sample_num: int) -> SWEBenchTask:
        """Create a synthetic task for a feature."""
        
        task_id = f"synthetic_{hashlib.md5(feature_desc.encode()).hexdigest()[:8]}_{sample_num}"
        
        return SWEBenchTask(
            instance_id=task_id,
            repo="synthetic/repo",
            base_commit="synthetic",
            problem_statement=f"Implement a {feature_desc}",
            hints_text=f"Focus on core functionality for {feature_desc}",
            created_at=datetime.now().isoformat(),
            version="1.0",
            FAIL_TO_PASS=[f"test_{feature_desc.replace(' ', '_').lower()}"],
            PASS_TO_PASS=["test_existing_functionality"]
        )
    
    async def collect_real_world_pairs(self, 
                                     github_repos: List[str],
                                     commit_patterns: List[str] = None) -> List[Dict[str, Any]]:
        """Collect real-world vibe-coded to production-ready pairs from GitHub.
        
        Args:
            github_repos: List of GitHub repositories to analyze
            commit_patterns: Patterns to identify refactoring commits
            
        Returns:
            Real-world dataset
        """
        if commit_patterns is None:
            commit_patterns = [
                "refactor",
                "cleanup", 
                "improve",
                "modularize",
                "production",
                "clean up"
            ]
        
        logger.info(f"Collecting real-world pairs from {len(github_repos)} repositories")
        
        dataset = []
        
        for repo in github_repos:
            try:
                repo_pairs = await self._analyze_repository_for_pairs(repo, commit_patterns)
                dataset.extend(repo_pairs)
                
                logger.info(f"Collected {len(repo_pairs)} pairs from {repo}")
                
            except Exception as e:
                logger.error(f"Failed to analyze repository {repo}: {str(e)}")
                continue
        
        # Save real-world dataset
        dataset_path = self._save_dataset(dataset, "real_world_vibe_production")
        logger.info(f"Saved {len(dataset)} real-world pairs to {dataset_path}")
        
        return dataset
    
    async def _analyze_repository_for_pairs(self, 
                                          repo: str,
                                          commit_patterns: List[str]) -> List[Dict[str, Any]]:
        """Analyze a repository for vibe-coded to production-ready transformations."""
        
        # This would integrate with GitHub API to find relevant commits
        # For now, return placeholder data
        
        logger.info(f"Analyzing repository {repo} for transformation pairs")
        
        # Placeholder implementation
        # In real implementation, this would:
        # 1. Use GitHub API to get commits matching patterns
        # 2. Analyze code changes in those commits
        # 3. Identify before/after code that represents vibe->production
        # 4. Extract the transformations as dataset entries
        
        return []