"""Human annotation system for evaluating code transformations."""

import json
import hashlib
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from enum import Enum

from ..utils import logger


class AnnotationQuality(str, Enum):
    """Quality levels for annotations."""
    EXCELLENT = "excellent"
    GOOD = "good"
    FAIR = "fair"
    POOR = "poor"


class TransformationType(str, Enum):
    """Types of code transformations."""
    MODULARITY_IMPROVEMENT = "modularity_improvement"
    ERROR_HANDLING_ADDED = "error_handling_added"
    TEST_COVERAGE_IMPROVED = "test_coverage_improved"
    CODE_CLEANUP = "code_cleanup"
    PERFORMANCE_OPTIMIZATION = "performance_optimization"
    SECURITY_IMPROVEMENT = "security_improvement"
    DOCUMENTATION_ADDED = "documentation_added"


class AnnotationSystem:
    """System for collecting and managing human annotations."""
    
    def __init__(self):
        self.annotations_dir = Path("data/annotations")
        self.annotations_dir.mkdir(parents=True, exist_ok=True)
        
    def create_annotation_task(self, 
                             vibe_code: str,
                             production_code: str,
                             context: Dict[str, Any]) -> str:
        """Create an annotation task for human reviewers.
        
        Args:
            vibe_code: Original vibe-coded implementation
            production_code: Production-ready transformed code
            context: Additional context about the transformation
            
        Returns:
            Task ID for the annotation
        """
        task_id = hashlib.md5(f"{vibe_code}_{production_code}_{datetime.now().isoformat()}".encode()).hexdigest()
        
        annotation_task = {
            "task_id": task_id,
            "created_at": datetime.now().isoformat(),
            "status": "pending",
            "vibe_code": vibe_code,
            "production_code": production_code,
            "context": context,
            "annotation_schema": self._get_annotation_schema(),
            "instructions": self._get_annotation_instructions()
        }
        
        # Save task
        task_file = self.annotations_dir / f"task_{task_id}.json"
        with open(task_file, 'w') as f:
            json.dump(annotation_task, f, indent=2)
        
        logger.info(f"Created annotation task {task_id}")
        return task_id
    
    def _get_annotation_schema(self) -> Dict[str, Any]:
        """Get the annotation schema for reviewers."""
        return {
            "overall_quality": {
                "type": "enum",
                "values": [q.value for q in AnnotationQuality],
                "description": "Overall quality of the transformation"
            },
            "transformation_types": {
                "type": "multi_select",
                "values": [t.value for t in TransformationType],
                "description": "Types of improvements made"
            },
            "modularity_improvement": {
                "type": "scale",
                "range": [1, 5],
                "description": "How much did modularity improve? (1=no improvement, 5=significant improvement)"
            },
            "readability_improvement": {
                "type": "scale", 
                "range": [1, 5],
                "description": "How much did readability improve?"
            },
            "maintainability_improvement": {
                "type": "scale",
                "range": [1, 5], 
                "description": "How much did maintainability improve?"
            },
            "test_coverage_improvement": {
                "type": "scale",
                "range": [1, 5],
                "description": "How much did test coverage improve?"
            },
            "error_handling_improvement": {
                "type": "scale",
                "range": [1, 5],
                "description": "How much did error handling improve?"
            },
            "production_readiness": {
                "type": "boolean",
                "description": "Is the transformed code production-ready?"
            },
            "missing_improvements": {
                "type": "text",
                "description": "What improvements are still missing?"
            },
            "specific_issues": {
                "type": "text",
                "description": "Specific issues found in the transformation"
            },
            "positive_aspects": {
                "type": "text",
                "description": "What was done well in the transformation?"
            },
            "confidence_score": {
                "type": "scale",
                "range": [1, 5],
                "description": "How confident are you in this annotation?"
            }
        }
    
    def _get_annotation_instructions(self) -> str:
        """Get instructions for human annotators."""
        return """
        # Code Transformation Annotation Instructions
        
        You are evaluating a transformation from "vibe-coded" (quick, working but not production-ready) code to "production-ready" code.
        
        ## What to Look For:
        
        ### Modularity Improvements:
        - Code split into logical functions/classes
        - Separation of concerns
        - Reusable components
        - Clear interfaces
        
        ### Code Quality Improvements:
        - Better naming conventions
        - Reduced complexity
        - Eliminated code duplication
        - Improved structure
        
        ### Error Handling:
        - Proper exception handling
        - Input validation
        - Graceful failure modes
        - Defensive programming
        
        ### Test Coverage:
        - Unit tests added
        - Edge cases covered
        - Integration tests
        - Clear test structure
        
        ### Production Readiness:
        - Performance considerations
        - Security best practices
        - Documentation
        - Configuration management
        
        ## Rating Scale:
        - 1: No improvement or made things worse
        - 2: Minimal improvement
        - 3: Moderate improvement
        - 4: Significant improvement
        - 5: Excellent improvement
        
        ## Quality Levels:
        - **Excellent**: Transformation significantly improves all aspects, production-ready
        - **Good**: Clear improvements in most areas, minor issues
        - **Fair**: Some improvements but significant gaps remain
        - **Poor**: Minimal improvement or introduces new problems
        """
    
    def submit_annotation(self, 
                         task_id: str,
                         annotation: Dict[str, Any],
                         annotator_id: str) -> bool:
        """Submit an annotation for a task.
        
        Args:
            task_id: ID of the annotation task
            annotation: The annotation data
            annotator_id: ID of the person providing the annotation
            
        Returns:
            True if annotation was saved successfully
        """
        try:
            # Validate annotation
            if not self._validate_annotation(annotation):
                logger.error(f"Invalid annotation format for task {task_id}")
                return False
            
            # Load existing task
            task_file = self.annotations_dir / f"task_{task_id}.json"
            if not task_file.exists():
                logger.error(f"Task {task_id} not found")
                return False
            
            # Save annotation
            annotation_data = {
                "task_id": task_id,
                "annotator_id": annotator_id,
                "submitted_at": datetime.now().isoformat(),
                "annotation": annotation
            }
            
            annotation_file = self.annotations_dir / f"annotation_{task_id}_{annotator_id}.json"
            with open(annotation_file, 'w') as f:
                json.dump(annotation_data, f, indent=2)
            
            # Update task status
            with open(task_file, 'r') as f:
                task_data = json.load(f)
            
            task_data["status"] = "annotated"
            task_data["annotated_at"] = datetime.now().isoformat()
            task_data["annotator_id"] = annotator_id
            
            with open(task_file, 'w') as f:
                json.dump(task_data, f, indent=2)
            
            logger.info(f"Annotation submitted for task {task_id} by {annotator_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to submit annotation: {str(e)}")
            return False
    
    def _validate_annotation(self, annotation: Dict[str, Any]) -> bool:
        """Validate annotation data against schema."""
        schema = self._get_annotation_schema()
        
        for field, field_schema in schema.items():
            if field not in annotation:
                logger.warning(f"Missing required field: {field}")
                return False
            
            value = annotation[field]
            field_type = field_schema["type"]
            
            if field_type == "enum":
                if value not in field_schema["values"]:
                    logger.warning(f"Invalid enum value for {field}: {value}")
                    return False
            
            elif field_type == "scale":
                if not isinstance(value, (int, float)) or not (field_schema["range"][0] <= value <= field_schema["range"][1]):
                    logger.warning(f"Invalid scale value for {field}: {value}")
                    return False
            
            elif field_type == "boolean":
                if not isinstance(value, bool):
                    logger.warning(f"Invalid boolean value for {field}: {value}")
                    return False
            
            elif field_type == "text":
                if not isinstance(value, str):
                    logger.warning(f"Invalid text value for {field}: {value}")
                    return False
        
        return True
    
    def get_annotation_statistics(self) -> Dict[str, Any]:
        """Get statistics about annotations."""
        
        # Count tasks and annotations
        task_files = list(self.annotations_dir.glob("task_*.json"))
        annotation_files = list(self.annotations_dir.glob("annotation_*.json"))
        
        total_tasks = len(task_files)
        total_annotations = len(annotation_files)
        
        # Load annotations and calculate statistics
        annotations = []
        for annotation_file in annotation_files:
            with open(annotation_file, 'r') as f:
                data = json.load(f)
                annotations.append(data["annotation"])
        
        if not annotations:
            return {
                "total_tasks": total_tasks,
                "total_annotations": total_annotations,
                "completion_rate": 0.0
            }
        
        # Calculate average scores
        avg_scores = {}
        score_fields = ["modularity_improvement", "readability_improvement", 
                       "maintainability_improvement", "test_coverage_improvement",
                       "error_handling_improvement", "confidence_score"]
        
        for field in score_fields:
            scores = [ann.get(field, 0) for ann in annotations if field in ann]
            avg_scores[f"avg_{field}"] = sum(scores) / len(scores) if scores else 0
        
        # Quality distribution
        quality_dist = {}
        for ann in annotations:
            quality = ann.get("overall_quality", "unknown")
            quality_dist[quality] = quality_dist.get(quality, 0) + 1
        
        # Production readiness rate
        production_ready = sum(1 for ann in annotations if ann.get("production_readiness", False))
        production_readiness_rate = production_ready / len(annotations) if annotations else 0
        
        return {
            "total_tasks": total_tasks,
            "total_annotations": total_annotations,
            "completion_rate": total_annotations / max(1, total_tasks),
            "average_scores": avg_scores,
            "quality_distribution": quality_dist,
            "production_readiness_rate": production_readiness_rate
        }
    
    def create_golden_dataset(self, 
                            min_agreement_threshold: float = 0.8,
                            min_annotations_per_item: int = 3) -> List[Dict[str, Any]]:
        """Create golden dataset from high-agreement annotations.
        
        Args:
            min_agreement_threshold: Minimum agreement required between annotators
            min_annotations_per_item: Minimum number of annotations per item
            
        Returns:
            Golden dataset with high-confidence annotations
        """
        logger.info("Creating golden dataset from annotations")
        
        # Group annotations by task
        task_annotations = {}
        annotation_files = list(self.annotations_dir.glob("annotation_*.json"))
        
        for annotation_file in annotation_files:
            with open(annotation_file, 'r') as f:
                data = json.load(f)
                task_id = data["task_id"]
                
                if task_id not in task_annotations:
                    task_annotations[task_id] = []
                
                task_annotations[task_id].append(data)
        
        golden_dataset = []
        
        for task_id, annotations in task_annotations.items():
            if len(annotations) < min_annotations_per_item:
                continue
            
            # Calculate agreement
            agreement_score = self._calculate_inter_annotator_agreement(annotations)
            
            if agreement_score >= min_agreement_threshold:
                # Load task data
                task_file = self.annotations_dir / f"task_{task_id}.json"
                with open(task_file, 'r') as f:
                    task_data = json.load(f)
                
                # Aggregate annotations
                aggregated_annotation = self._aggregate_annotations(annotations)
                
                golden_entry = {
                    "task_id": task_id,
                    "vibe_code": task_data["vibe_code"],
                    "production_code": task_data["production_code"],
                    "context": task_data["context"],
                    "annotation": aggregated_annotation,
                    "agreement_score": agreement_score,
                    "num_annotators": len(annotations)
                }
                
                golden_dataset.append(golden_entry)
        
        # Save golden dataset
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        golden_file = self.annotations_dir / f"golden_dataset_{timestamp}.json"
        
        with open(golden_file, 'w') as f:
            json.dump({
                "metadata": {
                    "created_at": datetime.now().isoformat(),
                    "min_agreement_threshold": min_agreement_threshold,
                    "min_annotations_per_item": min_annotations_per_item,
                    "num_items": len(golden_dataset)
                },
                "data": golden_dataset
            }, f, indent=2)
        
        logger.info(f"Created golden dataset with {len(golden_dataset)} items")
        return golden_dataset
    
    def _calculate_inter_annotator_agreement(self, annotations: List[Dict[str, Any]]) -> float:
        """Calculate agreement between annotators."""
        if len(annotations) < 2:
            return 1.0
        
        score_fields = ["modularity_improvement", "readability_improvement", 
                       "maintainability_improvement", "test_coverage_improvement",
                       "error_handling_improvement"]
        
        agreements = []
        
        for field in score_fields:
            scores = [ann["annotation"].get(field, 0) for ann in annotations if field in ann["annotation"]]
            if len(scores) >= 2:
                # Calculate standard deviation (lower = higher agreement)
                mean_score = sum(scores) / len(scores)
                variance = sum((score - mean_score) ** 2 for score in scores) / len(scores)
                std_dev = variance ** 0.5
                
                # Convert to agreement score (1 = perfect agreement, 0 = maximum disagreement)
                # Assuming scale of 1-5, max std dev would be around 2
                agreement = max(0, 1 - (std_dev / 2))
                agreements.append(agreement)
        
        return sum(agreements) / len(agreements) if agreements else 0.0
    
    def _aggregate_annotations(self, annotations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Aggregate multiple annotations into a single consensus annotation."""
        
        aggregated = {}
        
        # Aggregate numeric scores (average)
        score_fields = ["modularity_improvement", "readability_improvement", 
                       "maintainability_improvement", "test_coverage_improvement",
                       "error_handling_improvement", "confidence_score"]
        
        for field in score_fields:
            scores = [ann["annotation"].get(field, 0) for ann in annotations if field in ann["annotation"]]
            if scores:
                aggregated[field] = sum(scores) / len(scores)
        
        # Aggregate boolean fields (majority vote)
        bool_fields = ["production_readiness"]
        for field in bool_fields:
            values = [ann["annotation"].get(field, False) for ann in annotations if field in ann["annotation"]]
            if values:
                aggregated[field] = sum(values) / len(values) >= 0.5
        
        # Aggregate enum fields (mode)
        enum_fields = ["overall_quality"]
        for field in enum_fields:
            values = [ann["annotation"].get(field, "") for ann in annotations if field in ann["annotation"]]
            if values:
                # Find most common value
                value_counts = {}
                for value in values:
                    value_counts[value] = value_counts.get(value, 0) + 1
                aggregated[field] = max(value_counts, key=value_counts.get)
        
        # Aggregate text fields (concatenate unique responses)
        text_fields = ["missing_improvements", "specific_issues", "positive_aspects"]
        for field in text_fields:
            texts = [ann["annotation"].get(field, "") for ann in annotations if field in ann["annotation"] and ann["annotation"][field]]
            if texts:
                unique_texts = list(set(texts))
                aggregated[field] = " | ".join(unique_texts)
        
        return aggregated