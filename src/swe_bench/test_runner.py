"""Test execution and validation for SWE-bench tasks."""

import async<PERSON>
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..models import SWEBenchTask, CodeSolution, TestResult, EvaluationResult
from ..sandbox import E2BSandboxManager, DockerSandboxManager
from ..config import config
from ..utils import logger, eval_logger


class TestRunner:
    """Runs tests for SWE-bench tasks in sandboxed environments."""
    
    def __init__(self, use_e2b: bool = True):
        self.use_e2b = use_e2b
        if use_e2b:
            self.sandbox_manager = E2BSandboxManager()
        else:
            self.sandbox_manager = DockerSandboxManager()
    
    async def run_task_tests(self, 
                           task: SWEBenchTask, 
                           solution: CodeSolution,
                           task_id: Optional[str] = None) -> List[TestResult]:
        """Run all tests for a task with the given solution.
        
        Args:
            task: SWE-bench task
            solution: Generated code solution
            task_id: Optional task identifier for sandbox management
            
        Returns:
            List of test results
        """
        if not task_id:
            task_id = f"{task.instance_id}_{datetime.now().timestamp()}"
        
        try:
            # Create sandbox
            await self.sandbox_manager.create_sandbox(task_id)
            
            # Set up repository
            setup_success = await self.sandbox_manager.setup_repository(task_id, task)
            if not setup_success:
                raise Exception("Failed to set up repository in sandbox")
            
            # Run tests before applying solution (baseline)
            baseline_results = await self._run_baseline_tests(task_id, task)
            
            # Apply the solution
            apply_success = await self.sandbox_manager.apply_solution(task_id, solution)
            if not apply_success:
                raise Exception("Failed to apply solution to repository")
            
            # Run tests after applying solution
            solution_results = await self.sandbox_manager.run_tests(task_id, task)
            
            # Combine and analyze results
            final_results = self._analyze_test_results(
                task, baseline_results, solution_results
            )
            
            return final_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
        finally:
            # Always cleanup sandbox
            await self.sandbox_manager.cleanup_sandbox(task_id)
    
    async def _run_baseline_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests before applying solution to establish baseline."""
        
        try:
            baseline_results = await self.sandbox_manager.run_tests(task_id, task)
            
            logger.info(f"Baseline tests completed", 
                       task_id=task_id,
                       total_tests=len(baseline_results),
                       failed_tests=sum(1 for r in baseline_results if r.status == "FAILED"))
            
            return baseline_results
            
        except Exception as e:
            logger.warning(f"Baseline test execution failed", 
                          task_id=task_id, error=str(e))
            return []
    
    def _analyze_test_results(self, 
                            task: SWEBenchTask,
                            baseline_results: List[TestResult],
                            solution_results: List[TestResult]) -> List[TestResult]:
        """Analyze test results to determine if solution is correct.
        
        SWE-bench evaluation criteria:
        - FAIL_TO_PASS tests should fail in baseline and pass after solution
        - PASS_TO_PASS tests should pass in both baseline and after solution
        """
        
        # Create lookup for baseline results
        baseline_lookup = {r.test_name: r for r in baseline_results}
        
        analyzed_results = []
        
        for result in solution_results:
            test_name = result.test_name
            baseline_result = baseline_lookup.get(test_name)
            
            # Determine if this test behaved correctly
            is_correct = self._is_test_result_correct(
                task, test_name, baseline_result, result
            )
            
            # Add metadata to result
            analyzed_result = TestResult(
                test_name=result.test_name,
                status=result.status,
                output=result.output,
                execution_time=result.execution_time,
                error_message=result.error_message
            )
            
            # Add correctness information to output
            correctness_info = f"\n[EVALUATION] Test correctness: {'CORRECT' if is_correct else 'INCORRECT'}"
            if baseline_result:
                correctness_info += f" (Baseline: {baseline_result.status} -> Solution: {result.status})"
            analyzed_result.output += correctness_info
            
            analyzed_results.append(analyzed_result)
        
        return analyzed_results
    
    def _is_test_result_correct(self, 
                              task: SWEBenchTask,
                              test_name: str,
                              baseline_result: Optional[TestResult],
                              solution_result: TestResult) -> bool:
        """Determine if a test result is correct according to SWE-bench criteria."""
        
        if test_name in task.FAIL_TO_PASS:
            # This test should fail in baseline and pass after solution
            if not baseline_result:
                # No baseline - assume it was failing
                return solution_result.status == "PASSED"
            
            return (baseline_result.status in ["FAILED", "ERROR"] and 
                   solution_result.status == "PASSED")
        
        elif test_name in task.PASS_TO_PASS:
            # This test should pass in both baseline and after solution
            if not baseline_result:
                # No baseline - just check if it passes now
                return solution_result.status == "PASSED"
            
            return (baseline_result.status == "PASSED" and 
                   solution_result.status == "PASSED")
        
        else:
            # Test not in expected lists - consider it correct if it passes
            return solution_result.status == "PASSED"
    
    async def validate_solution(self, 
                              task: SWEBenchTask, 
                              solution: CodeSolution) -> Dict[str, Any]:
        """Validate a solution against SWE-bench criteria.
        
        Returns:
            Dictionary with validation results
        """
        
        test_results = await self.run_task_tests(task, solution)
        
        # Calculate metrics
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r.status == "PASSED")
        failed_tests = sum(1 for r in test_results if r.status == "FAILED")
        error_tests = sum(1 for r in test_results if r.status == "ERROR")
        
        # Check FAIL_TO_PASS tests specifically
        fail_to_pass_results = [r for r in test_results if r.test_name in task.FAIL_TO_PASS]
        fail_to_pass_passed = sum(1 for r in fail_to_pass_results if r.status == "PASSED")
        
        # Check PASS_TO_PASS tests specifically
        pass_to_pass_results = [r for r in test_results if r.test_name in task.PASS_TO_PASS]
        pass_to_pass_passed = sum(1 for r in pass_to_pass_results if r.status == "PASSED")
        
        # Determine overall success
        fail_to_pass_success = (len(task.FAIL_TO_PASS) == 0 or 
                               fail_to_pass_passed == len(task.FAIL_TO_PASS))
        pass_to_pass_success = (len(task.PASS_TO_PASS) == 0 or 
                               pass_to_pass_passed == len(task.PASS_TO_PASS))
        
        overall_success = fail_to_pass_success and pass_to_pass_success
        
        validation_result = {
            "overall_success": overall_success,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": passed_tests / max(1, total_tests),
            
            # SWE-bench specific metrics
            "fail_to_pass_total": len(task.FAIL_TO_PASS),
            "fail_to_pass_passed": fail_to_pass_passed,
            "fail_to_pass_success": fail_to_pass_success,
            
            "pass_to_pass_total": len(task.PASS_TO_PASS),
            "pass_to_pass_passed": pass_to_pass_passed,
            "pass_to_pass_success": pass_to_pass_success,
            
            "test_results": test_results
        }
        
        # Log validation results
        eval_logger.logger.info(
            "Solution validation completed",
            task_id=task.instance_id,
            overall_success=overall_success,
            success_rate=validation_result["success_rate"],
            **{k: v for k, v in validation_result.items() 
               if k not in ["test_results", "overall_success", "success_rate"]}
        )
        
        return validation_result
    
    async def batch_validate_solutions(self, 
                                     tasks_and_solutions: List[tuple[SWEBenchTask, CodeSolution]],
                                     max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """Validate multiple solutions concurrently.
        
        Args:
            tasks_and_solutions: List of (task, solution) tuples
            max_concurrent: Maximum concurrent validations
            
        Returns:
            List of validation results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def validate_with_semaphore(task_solution_pair):
            task, solution = task_solution_pair
            async with semaphore:
                return await self.validate_solution(task, solution)
        
        # Create validation tasks
        validation_tasks = [
            validate_with_semaphore(pair) for pair in tasks_and_solutions
        ]
        
        # Execute all validations concurrently
        results = await asyncio.gather(*validation_tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                task, _ = tasks_and_solutions[i]
                error_result = {
                    "overall_success": False,
                    "error": str(result),
                    "task_id": task.instance_id
                }
                final_results.append(error_result)
            else:
                final_results.append(result)
        
        return final_results
