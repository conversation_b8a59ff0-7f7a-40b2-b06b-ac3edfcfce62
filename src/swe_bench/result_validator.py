"""Result validation and analysis for SWE-bench evaluations."""

from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from ..models import (
    EvaluationResult, 
    EvaluationSummary, 
    ComparisonReport,
    EvaluatorType,
    TaskStatus
)
from ..config import config
from ..utils import logger


class ResultValidator:
    """Validates and analyzes evaluation results."""
    
    def __init__(self):
        self.results_path = config.results_output_path
    
    def validate_evaluation_result(self, result: EvaluationResult) -> Dict[str, Any]:
        """Validate a single evaluation result.
        
        Args:
            result: Evaluation result to validate
            
        Returns:
            Validation report
        """
        validation_report = {
            "task_id": result.task_id,
            "evaluator_type": result.evaluator_type.value,
            "is_valid": True,
            "issues": [],
            "metrics": {}
        }
        
        # Check basic validity
        if result.status == TaskStatus.COMPLETED:
            if not result.solution:
                validation_report["is_valid"] = False
                validation_report["issues"].append("Completed task missing solution")
            
            if not result.test_results:
                validation_report["issues"].append("No test results available")
            
            # Calculate SWE-bench specific metrics
            if result.test_results:
                metrics = self._calculate_swe_bench_metrics(result)
                validation_report["metrics"] = metrics
        
        elif result.status == TaskStatus.FAILED:
            if not result.error_message:
                validation_report["issues"].append("Failed task missing error message")
        
        # Check cost reasonableness
        if result.api_cost > 10.0:  # $10 seems high for a single task
            validation_report["issues"].append(f"High API cost: ${result.api_cost:.2f}")
        
        # Check execution time
        if result.execution_time > 600:  # 10 minutes seems long
            validation_report["issues"].append(f"Long execution time: {result.execution_time:.1f}s")
        
        return validation_report
    
    def _calculate_swe_bench_metrics(self, result: EvaluationResult) -> Dict[str, Any]:
        """Calculate SWE-bench specific metrics from test results."""
        
        if not result.test_results:
            return {}
        
        total_tests = len(result.test_results)
        passed_tests = sum(1 for r in result.test_results if r.status == "PASSED")
        failed_tests = sum(1 for r in result.test_results if r.status == "FAILED")
        error_tests = sum(1 for r in result.test_results if r.status == "ERROR")
        
        # Extract SWE-bench correctness from test outputs
        correct_tests = 0
        for test_result in result.test_results:
            if "[EVALUATION] Test correctness: CORRECT" in test_result.output:
                correct_tests += 1
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "correct_tests": correct_tests,
            "pass_rate": passed_tests / max(1, total_tests),
            "correctness_rate": correct_tests / max(1, total_tests),
            "swe_bench_success": correct_tests == total_tests and total_tests > 0
        }
    
    def create_evaluation_summary(self, 
                                results: List[EvaluationResult],
                                evaluator_type: EvaluatorType) -> EvaluationSummary:
        """Create summary from evaluation results.
        
        Args:
            results: List of evaluation results
            evaluator_type: Type of evaluator
            
        Returns:
            Evaluation summary
        """
        
        total_tasks = len(results)
        completed_tasks = sum(1 for r in results if r.status == TaskStatus.COMPLETED)
        failed_tasks = sum(1 for r in results if r.status == TaskStatus.FAILED)
        
        # Aggregate metrics
        total_tests_passed = 0
        total_tests_failed = 0
        total_execution_time = 0.0
        total_api_cost = 0.0
        
        for result in results:
            if result.test_results:
                total_tests_passed += sum(1 for r in result.test_results if r.status == "PASSED")
                total_tests_failed += sum(1 for r in result.test_results if r.status == "FAILED")
            
            total_execution_time += result.execution_time
            total_api_cost += result.api_cost
        
        summary = EvaluationSummary(
            evaluator_type=evaluator_type,
            total_tasks=total_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            total_tests_passed=total_tests_passed,
            total_tests_failed=total_tests_failed,
            total_execution_time=total_execution_time,
            total_api_cost=total_api_cost,
            results=results
        )
        
        return summary
    
    def create_comparison_report(self, 
                               summaries: Dict[EvaluatorType, EvaluationSummary]) -> ComparisonReport:
        """Create comparison report from multiple evaluation summaries.
        
        Args:
            summaries: Dictionary of evaluator summaries
            
        Returns:
            Comparison report
        """
        
        report = ComparisonReport(summaries=summaries)
        
        logger.info(f"Created comparison report", 
                   evaluators=list(summaries.keys()),
                   total_evaluators=len(summaries))
        
        return report
    
    def save_results(self, 
                    results: List[EvaluationResult],
                    filename: Optional[str] = None) -> str:
        """Save evaluation results to file.
        
        Args:
            results: List of evaluation results
            filename: Optional filename (auto-generated if not provided)
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_results_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert results to serializable format
        serializable_results = []
        for result in results:
            result_dict = result.dict()
            # Convert datetime objects to strings
            if result_dict.get('started_at'):
                result_dict['started_at'] = result_dict['started_at'].isoformat()
            if result_dict.get('completed_at'):
                result_dict['completed_at'] = result_dict['completed_at'].isoformat()
            serializable_results.append(result_dict)
        
        with open(filepath, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        logger.info(f"Saved evaluation results", 
                   filepath=str(filepath), 
                   num_results=len(results))
        
        return str(filepath)
    
    def save_summary(self, 
                    summary: EvaluationSummary,
                    filename: Optional[str] = None) -> str:
        """Save evaluation summary to file.
        
        Args:
            summary: Evaluation summary
            filename: Optional filename
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            evaluator = summary.evaluator_type.value
            filename = f"evaluation_summary_{evaluator}_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert to serializable format
        summary_dict = summary.dict()
        
        with open(filepath, 'w') as f:
            json.dump(summary_dict, f, indent=2)
        
        logger.info(f"Saved evaluation summary", 
                   filepath=str(filepath),
                   evaluator_type=summary.evaluator_type.value)
        
        return str(filepath)
    
    def save_comparison_report(self, 
                             report: ComparisonReport,
                             filename: Optional[str] = None) -> str:
        """Save comparison report to file.
        
        Args:
            report: Comparison report
            filename: Optional filename
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comparison_report_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert to serializable format
        report_dict = report.dict()
        report_dict['generated_at'] = report_dict['generated_at'].isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(report_dict, f, indent=2)
        
        logger.info(f"Saved comparison report", 
                   filepath=str(filepath),
                   num_evaluators=len(report.summaries))
        
        return str(filepath)
    
    def load_results(self, filepath: str) -> List[EvaluationResult]:
        """Load evaluation results from file.
        
        Args:
            filepath: Path to results file
            
        Returns:
            List of evaluation results
        """
        
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        results = []
        for item in data:
            # Convert datetime strings back to datetime objects
            if item.get('started_at'):
                item['started_at'] = datetime.fromisoformat(item['started_at'])
            if item.get('completed_at'):
                item['completed_at'] = datetime.fromisoformat(item['completed_at'])
            
            result = EvaluationResult(**item)
            results.append(result)
        
        logger.info(f"Loaded evaluation results", 
                   filepath=filepath, 
                   num_results=len(results))
        
        return results
    
    def generate_report_summary(self, report: ComparisonReport) -> str:
        """Generate a human-readable summary of the comparison report.
        
        Args:
            report: Comparison report
            
        Returns:
            Human-readable summary string
        """
        
        lines = []
        lines.append("=== SWE-bench Evaluation Comparison Report ===")
        lines.append(f"Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # Overall comparison
        lines.append("Overall Performance:")
        for evaluator_type, summary in report.summaries.items():
            lines.append(f"  {evaluator_type.value}:")
            lines.append(f"    Completion Rate: {summary.completion_rate:.1%}")
            lines.append(f"    Success Rate: {summary.overall_success_rate:.1%}")
            lines.append(f"    Total Cost: ${summary.total_api_cost:.2f}")
            lines.append(f"    Avg Time/Task: {summary.average_execution_time:.1f}s")
            lines.append("")
        
        # Best performers
        best_success = report.get_best_performer("success_rate")
        best_completion = report.get_best_performer("completion_rate")
        best_cost = report.get_best_performer("cost_efficiency")
        
        lines.append("Best Performers:")
        if best_success:
            lines.append(f"  Highest Success Rate: {best_success.value}")
        if best_completion:
            lines.append(f"  Highest Completion Rate: {best_completion.value}")
        if best_cost:
            lines.append(f"  Most Cost Efficient: {best_cost.value}")
        
        return "\n".join(lines)
