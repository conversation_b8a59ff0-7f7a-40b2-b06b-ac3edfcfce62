"""Background agent for code refactoring and modularization."""

import asyncio
import ast
import re
from typing import Dict, Any, List, Optional
from pathlib import Path

from ..evaluators import ClaudeE<PERSON><PERSON><PERSON>
from ..models import CodeSolution
from ..utils import logger


class CodeRefactorAgent:
    """Background agent that refactors vibe-coded solutions into modular, production-ready code."""
    
    def __init__(self):
        self.evaluator = ClaudeEvaluator()
        
    async def productionize_code(self, 
                               vibe_code: str,
                               context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Transform vibe-coded solution into production-ready code.
        
        Args:
            vibe_code: Original vibe-coded implementation
            context: Additional context about the code (framework, requirements, etc.)
            
        Returns:
            Dictionary containing refactored code and analysis
        """
        logger.info("Starting code productionization")
        
        # Analyze the vibe code
        analysis = self._analyze_vibe_code(vibe_code)
        
        # Create refactoring plan
        refactor_plan = self._create_refactor_plan(analysis, context or {})
        
        # Execute refactoring steps
        refactored_code = await self._execute_refactoring(vibe_code, refactor_plan)
        
        # Validate the refactored code
        validation_results = self._validate_refactored_code(vibe_code, refactored_code)
        
        return {
            "original_code": vibe_code,
            "refactored_code": refactored_code,
            "analysis": analysis,
            "refactor_plan": refactor_plan,
            "validation": validation_results,
            "improvements": self._calculate_improvements(analysis, validation_results)
        }
    
    def _analyze_vibe_code(self, code: str) -> Dict[str, Any]:
        """Analyze vibe code to identify issues and improvement opportunities."""
        
        analysis = {
            "issues": [],
            "opportunities": [],
            "complexity_metrics": {},
            "structure_analysis": {}
        }
        
        try:
            tree = ast.parse(code)
            
            # Analyze structure
            analysis["structure_analysis"] = {
                "functions": self._count_functions(tree),
                "classes": self._count_classes(tree),
                "imports": self._count_imports(tree),
                "lines_of_code": len(code.split('\n'))
            }
            
            # Identify issues
            analysis["issues"] = self._identify_code_issues(tree, code)
            
            # Find opportunities
            analysis["opportunities"] = self._identify_refactor_opportunities(tree, code)
            
            # Calculate complexity
            analysis["complexity_metrics"] = self._calculate_complexity_metrics(tree)
            
        except SyntaxError as e:
            analysis["issues"].append({
                "type": "syntax_error",
                "description": f"Syntax error in code: {str(e)}",
                "severity": "high"
            })
        
        return analysis
    
    def _count_functions(self, tree: ast.AST) -> int:
        """Count functions in AST."""
        return len([node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)])
    
    def _count_classes(self, tree: ast.AST) -> int:
        """Count classes in AST."""
        return len([node for node in ast.walk(tree) if isinstance(node, ast.ClassDef)])
    
    def _count_imports(self, tree: ast.AST) -> int:
        """Count import statements."""
        return len([node for node in ast.walk(tree) if isinstance(node, (ast.Import, ast.ImportFrom))])
    
    def _identify_code_issues(self, tree: ast.AST, code: str) -> List[Dict[str, Any]]:
        """Identify issues in the vibe code."""
        issues = []
        
        # Long functions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                length = node.end_lineno - node.lineno + 1
                if length > 50:
                    issues.append({
                        "type": "long_function",
                        "description": f"Function '{node.name}' is {length} lines long",
                        "severity": "medium",
                        "function": node.name,
                        "line_count": length
                    })
        
        # Missing error handling
        functions_with_try = set()
        for node in ast.walk(tree):
            if isinstance(node, ast.Try):
                # Find parent function
                for parent in ast.walk(tree):
                    if isinstance(parent, ast.FunctionDef):
                        if (hasattr(parent, 'lineno') and hasattr(node, 'lineno') and
                            parent.lineno <= node.lineno <= parent.end_lineno):
                            functions_with_try.add(parent.name)
        
        all_functions = [node.name for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        for func_name in all_functions:
            if func_name not in functions_with_try:
                issues.append({
                    "type": "missing_error_handling",
                    "description": f"Function '{func_name}' lacks error handling",
                    "severity": "medium",
                    "function": func_name
                })
        
        # Code duplication
        lines = code.split('\n')
        line_counts = {}
        for line in lines:
            stripped = line.strip()
            if stripped and not stripped.startswith('#'):
                line_counts[stripped] = line_counts.get(stripped, 0) + 1
        
        duplicated_lines = [line for line, count in line_counts.items() if count > 1]
        if duplicated_lines:
            issues.append({
                "type": "code_duplication",
                "description": f"Found {len(duplicated_lines)} duplicated lines",
                "severity": "low",
                "duplicated_lines": len(duplicated_lines)
            })
        
        # Poor naming conventions
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                if not re.match(r'^[a-z_][a-z0-9_]*$', node.name):
                    issues.append({
                        "type": "naming_convention",
                        "description": f"Function '{node.name}' doesn't follow snake_case convention",
                        "severity": "low",
                        "function": node.name
                    })
        
        return issues
    
    def _identify_refactor_opportunities(self, tree: ast.AST, code: str) -> List[Dict[str, Any]]:
        """Identify refactoring opportunities."""
        opportunities = []
        
        # Extract components opportunity
        if self._has_mixed_concerns(tree, code):
            opportunities.append({
                "type": "extract_components",
                "description": "Code mixes multiple concerns - can extract separate components",
                "benefit": "Improved separation of concerns and modularity"
            })
        
        # Extract utility functions
        if self._has_repeated_patterns(code):
            opportunities.append({
                "type": "extract_utilities",
                "description": "Repeated code patterns can be extracted into utility functions",
                "benefit": "Reduced duplication and improved reusability"
            })
        
        # Add configuration management
        if self._has_hardcoded_values(tree):
            opportunities.append({
                "type": "extract_configuration",
                "description": "Hardcoded values can be moved to configuration",
                "benefit": "Improved maintainability and environment flexibility"
            })
        
        # Add input validation
        if self._lacks_input_validation(tree):
            opportunities.append({
                "type": "add_validation",
                "description": "Functions lack input validation",
                "benefit": "Improved robustness and error prevention"
            })
        
        return opportunities
    
    def _has_mixed_concerns(self, tree: ast.AST, code: str) -> bool:
        """Check if code mixes different concerns."""
        concerns = {
            "ui": bool(re.search(r'render|jsx|html|css|style', code, re.IGNORECASE)),
            "business": bool(re.search(r'calculate|process|validate|transform', code, re.IGNORECASE)),
            "data": bool(re.search(r'fetch|api|database|sql', code, re.IGNORECASE))
        }
        return sum(concerns.values()) > 1
    
    def _has_repeated_patterns(self, code: str) -> bool:
        """Check for repeated code patterns."""
        lines = [line.strip() for line in code.split('\n') if line.strip()]
        unique_lines = set(lines)
        return len(lines) - len(unique_lines) > 3
    
    def _has_hardcoded_values(self, tree: ast.AST) -> bool:
        """Check for hardcoded values that should be configurable."""
        for node in ast.walk(tree):
            if isinstance(node, ast.Constant):
                if isinstance(node.value, str) and len(node.value) > 10:
                    return True
                if isinstance(node.value, (int, float)) and node.value > 100:
                    return True
        return False
    
    def _lacks_input_validation(self, tree: ast.AST) -> bool:
        """Check if functions lack input validation."""
        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Check if function has validation (isinstance, assert, if checks)
                has_validation = False
                for child in ast.walk(node):
                    if isinstance(child, (ast.Assert, ast.If)):
                        has_validation = True
                        break
                    if (isinstance(child, ast.Call) and 
                        hasattr(child.func, 'id') and 
                        child.func.id in ['isinstance', 'type']):
                        has_validation = True
                        break
                
                if not has_validation and len(node.args.args) > 0:
                    return True
        return False
    
    def _calculate_complexity_metrics(self, tree: ast.AST) -> Dict[str, float]:
        """Calculate complexity metrics."""
        return {
            "cyclomatic_complexity": self._cyclomatic_complexity(tree),
            "nesting_depth": self._max_nesting_depth(tree),
            "function_complexity": self._average_function_complexity(tree)
        }
    
    def _cyclomatic_complexity(self, tree: ast.AST) -> int:
        """Calculate cyclomatic complexity."""
        complexity = 1  # Base complexity
        for node in ast.walk(tree):
            if isinstance(node, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(node, ast.BoolOp):
                complexity += len(node.values) - 1
        return complexity
    
    def _max_nesting_depth(self, tree: ast.AST) -> int:
        """Calculate maximum nesting depth."""
        def get_depth(node, current_depth=0):
            max_depth = current_depth
            for child in ast.iter_child_nodes(node):
                if isinstance(child, (ast.If, ast.While, ast.For, ast.Try, ast.With)):
                    child_depth = get_depth(child, current_depth + 1)
                    max_depth = max(max_depth, child_depth)
                else:
                    child_depth = get_depth(child, current_depth)
                    max_depth = max(max_depth, child_depth)
            return max_depth
        
        return get_depth(tree)
    
    def _average_function_complexity(self, tree: ast.AST) -> float:
        """Calculate average function complexity."""
        functions = [node for node in ast.walk(tree) if isinstance(node, ast.FunctionDef)]
        if not functions:
            return 0.0
        
        total_complexity = sum(self._cyclomatic_complexity(func) for func in functions)
        return total_complexity / len(functions)
    
    def _create_refactor_plan(self, analysis: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """Create a refactoring plan based on analysis."""
        
        plan = {
            "steps": [],
            "priority": "medium",
            "estimated_effort": "medium"
        }
        
        # Prioritize based on severity and impact
        high_priority_issues = [issue for issue in analysis["issues"] if issue["severity"] == "high"]
        if high_priority_issues:
            plan["priority"] = "high"
        
        # Add refactoring steps based on issues and opportunities
        for issue in analysis["issues"]:
            if issue["type"] == "long_function":
                plan["steps"].append({
                    "action": "extract_functions",
                    "target": issue["function"],
                    "description": f"Break down long function '{issue['function']}' into smaller functions"
                })
            
            elif issue["type"] == "missing_error_handling":
                plan["steps"].append({
                    "action": "add_error_handling",
                    "target": issue["function"],
                    "description": f"Add error handling to function '{issue['function']}'"
                })
        
        for opportunity in analysis["opportunities"]:
            if opportunity["type"] == "extract_components":
                plan["steps"].append({
                    "action": "modularize_code",
                    "description": "Extract separate components for different concerns"
                })
            
            elif opportunity["type"] == "extract_utilities":
                plan["steps"].append({
                    "action": "create_utilities",
                    "description": "Extract common functionality into utility functions"
                })
        
        # Framework-specific steps
        framework = context.get("framework", "").lower()
        if "react" in framework:
            plan["steps"].append({
                "action": "extract_react_components",
                "description": "Extract React components from monolithic code"
            })
        
        return plan
    
    async def _execute_refactoring(self, vibe_code: str, refactor_plan: Dict[str, Any]) -> CodeSolution:
        """Execute the refactoring plan using Claude."""
        
        refactor_prompt = f"""
You are a senior software engineer refactoring vibe-coded solution into production-ready code.

Original Code:
{vibe_code}

Refactoring Plan:
{self._format_refactor_plan(refactor_plan)}

Transform this code following the refactoring plan. Focus on:
1. Modularity and separation of concerns
2. Error handling and input validation
3. Clean, readable code structure
4. Best practices for the identified framework/language
5. Extracting reusable components

Provide the refactored code with proper organization and structure.
"""
        
        # Create a temporary task for refactoring
        from ..models import SWEBenchTask
        refactor_task = SWEBenchTask(
            instance_id="refactor_task",
            repo="refactor/temp",
            base_commit="temp",
            problem_statement=refactor_prompt,
            hints_text="Focus on production-ready best practices",
            created_at="2024-01-01",
            version="1.0"
        )
        
        return await self.evaluator.generate_solution(refactor_task)
    
    def _format_refactor_plan(self, plan: Dict[str, Any]) -> str:
        """Format refactor plan for prompt."""
        formatted = f"Priority: {plan['priority']}\n\nSteps:\n"
        for i, step in enumerate(plan["steps"], 1):
            formatted += f"{i}. {step['action']}: {step['description']}\n"
        return formatted
    
    def _validate_refactored_code(self, original_code: str, refactored_solution: CodeSolution) -> Dict[str, Any]:
        """Validate the refactored code."""
        
        validation = {
            "syntax_valid": True,
            "improvements": {},
            "issues": []
        }
        
        try:
            # Check if refactored code is syntactically valid
            ast.parse(refactored_solution.code)
        except SyntaxError as e:
            validation["syntax_valid"] = False
            validation["issues"].append(f"Syntax error: {str(e)}")
        
        if validation["syntax_valid"]:
            # Compare metrics
            original_analysis = self._analyze_vibe_code(original_code)
            refactored_analysis = self._analyze_vibe_code(refactored_solution.code)
            
            validation["improvements"] = {
                "complexity_reduction": (
                    original_analysis["complexity_metrics"]["cyclomatic_complexity"] - 
                    refactored_analysis["complexity_metrics"]["cyclomatic_complexity"]
                ),
                "issues_resolved": len(original_analysis["issues"]) - len(refactored_analysis["issues"]),
                "function_count_change": (
                    refactored_analysis["structure_analysis"]["functions"] - 
                    original_analysis["structure_analysis"]["functions"]
                )
            }
        
        return validation
    
    def _calculate_improvements(self, analysis: Dict[str, Any], validation: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate overall improvements from refactoring."""
        
        if not validation["syntax_valid"]:
            return {"overall_score": 0.0, "details": "Refactoring failed - syntax errors"}
        
        improvements = validation["improvements"]
        
        # Calculate improvement score
        score = 0.0
        
        # Complexity reduction (up to 30 points)
        if improvements["complexity_reduction"] > 0:
            score += min(30, improvements["complexity_reduction"] * 5)
        
        # Issues resolved (up to 40 points)
        if improvements["issues_resolved"] > 0:
            score += min(40, improvements["issues_resolved"] * 10)
        
        # Modularization (up to 30 points)
        if improvements["function_count_change"] > 0:
            score += min(30, improvements["function_count_change"] * 3)
        
        return {
            "overall_score": min(100, score),
            "complexity_improvement": improvements["complexity_reduction"],
            "issues_resolved": improvements["issues_resolved"],
            "modularization_improvement": improvements["function_count_change"],
            "details": f"Resolved {improvements['issues_resolved']} issues, reduced complexity by {improvements['complexity_reduction']}"
        }