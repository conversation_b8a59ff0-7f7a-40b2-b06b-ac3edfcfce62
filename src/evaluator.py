"""Main evaluation orchestrator for SWE-bench tasks."""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime

from .models import (
    SWEBenchTask, 
    EvaluationResult, 
    EvaluationSummary,
    ComparisonReport,
    EvaluatorType,
    TaskStatus
)
from .evaluators import ClaudeE<PERSON>uator, CodexEvaluator, CodeGenEvaluator
from .swe_bench import <PERSON><PERSON><PERSON><PERSON><PERSON>ch<PERSON>oader, TestRunner, ResultValidator
from .utils import logger, eval_logger, budget_tracker
from .config import config


class SWEBenchEvaluator:
    """Main orchestrator for SWE-bench evaluations."""
    
    def __init__(self):
        self.task_loader = SWEBenchLoader()
        self.test_runner = TestRunner(use_e2b=True)  # Prefer E2B over Docker
        self.result_validator = ResultValidator()
        
        # Initialize evaluators
        self.evaluators = {
            EvaluatorType.CLAUDE: ClaudeEvaluator(),
            EvaluatorType.CODEX: CodexEvaluator(),
            EvaluatorType.CODEGEN: CodeGenEvaluator()
        }
        
        # Update budget tracker with config
        budget_tracker.max_budget = config.max_budget
    
    async def evaluate_single_task(self, 
                                 task: SWEBenchTask,
                                 evaluator_type: EvaluatorType) -> EvaluationResult:
        """Evaluate a single task with a specific evaluator.
        
        Args:
            task: SWE-bench task to evaluate
            evaluator_type: Type of evaluator to use
            
        Returns:
            Evaluation result
        """
        
        evaluator = self.evaluators[evaluator_type]
        
        try:
            # Generate solution using the evaluator
            result = await evaluator.evaluate_task(task)
            
            # If solution was generated successfully, run tests
            if result.status == TaskStatus.COMPLETED and result.solution:
                # Run tests to validate the solution
                validation_results = await self.test_runner.validate_solution(
                    task, result.solution
                )
                
                # Update result with test information
                result.test_results = validation_results.get("test_results", [])
                result.tests_passed = validation_results.get("passed_tests", 0)
                result.tests_failed = validation_results.get("failed_tests", 0)
                
                # Log final result
                eval_logger.task_completed(
                    task.instance_id,
                    evaluator_type.value,
                    result.success_rate,
                    result.execution_time,
                    result.api_cost
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Task evaluation failed", 
                        task_id=task.instance_id,
                        evaluator_type=evaluator_type.value,
                        error=str(e))
            
            # Return failed result
            return EvaluationResult(
                task_id=task.instance_id,
                evaluator_type=evaluator_type,
                status=TaskStatus.FAILED,
                error_message=str(e)
            )
    
    async def evaluate_tasks_batch(self,
                                 tasks: List[SWEBenchTask],
                                 evaluator_type: EvaluatorType,
                                 max_concurrent: int = 3) -> List[EvaluationResult]:
        """Evaluate multiple tasks with a single evaluator.
        
        Args:
            tasks: List of tasks to evaluate
            evaluator_type: Type of evaluator to use
            max_concurrent: Maximum concurrent evaluations
            
        Returns:
            List of evaluation results
        """
        
        logger.info(f"Starting batch evaluation", 
                   evaluator_type=evaluator_type.value,
                   num_tasks=len(tasks),
                   max_concurrent=max_concurrent)
        
        # Check budget before starting
        evaluator = self.evaluators[evaluator_type]
        total_estimated_cost = sum(evaluator.estimate_cost(task) for task in tasks)
        
        if not budget_tracker.can_afford(total_estimated_cost):
            raise Exception(f"Insufficient budget. Need ${total_estimated_cost:.2f}, "
                          f"have ${budget_tracker.get_remaining_budget():.2f}")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def evaluate_with_semaphore(task: SWEBenchTask) -> EvaluationResult:
            async with semaphore:
                # Check budget before each task
                if budget_tracker.is_budget_exceeded():
                    return EvaluationResult(
                        task_id=task.instance_id,
                        evaluator_type=evaluator_type,
                        status=TaskStatus.FAILED,
                        error_message="Budget exceeded"
                    )
                
                return await self.evaluate_single_task(task, evaluator_type)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(
            *[evaluate_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_result = EvaluationResult(
                    task_id=tasks[i].instance_id,
                    evaluator_type=evaluator_type,
                    status=TaskStatus.FAILED,
                    error_message=str(result)
                )
                final_results.append(failed_result)
            else:
                final_results.append(result)
        
        logger.info(f"Batch evaluation completed", 
                   evaluator_type=evaluator_type.value,
                   total_tasks=len(final_results),
                   completed_tasks=sum(1 for r in final_results if r.status == TaskStatus.COMPLETED),
                   failed_tasks=sum(1 for r in final_results if r.status == TaskStatus.FAILED))
        
        return final_results
    
    async def compare_evaluators(self,
                               tasks: List[SWEBenchTask],
                               evaluator_types: List[EvaluatorType],
                               max_concurrent: int = 2) -> ComparisonReport:
        """Compare multiple evaluators on the same set of tasks.
        
        Args:
            tasks: List of tasks to evaluate
            evaluator_types: List of evaluator types to compare
            max_concurrent: Maximum concurrent evaluations per evaluator
            
        Returns:
            Comparison report
        """
        
        logger.info(f"Starting evaluator comparison", 
                   evaluator_types=[e.value for e in evaluator_types],
                   num_tasks=len(tasks))
        
        summaries = {}
        
        for evaluator_type in evaluator_types:
            try:
                logger.info(f"Evaluating with {evaluator_type.value}")
                
                # Evaluate tasks with this evaluator
                results = await self.evaluate_tasks_batch(
                    tasks, evaluator_type, max_concurrent
                )
                
                # Create summary
                summary = self.result_validator.create_evaluation_summary(
                    results, evaluator_type
                )
                summaries[evaluator_type] = summary
                
                # Save individual results
                self.result_validator.save_results(
                    results, 
                    f"results_{evaluator_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                
                # Log summary
                eval_logger.evaluation_summary(
                    evaluator_type.value,
                    {
                        "completion_rate": summary.completion_rate,
                        "success_rate": summary.overall_success_rate,
                        "total_cost": summary.total_api_cost,
                        "avg_time": summary.average_execution_time
                    }
                )
                
            except Exception as e:
                logger.error(f"Evaluator comparison failed", 
                           evaluator_type=evaluator_type.value,
                           error=str(e))
                continue
        
        # Create comparison report
        report = self.result_validator.create_comparison_report(summaries)
        
        # Save comparison report
        report_path = self.result_validator.save_comparison_report(report)
        
        # Generate and log summary
        summary_text = self.result_validator.generate_report_summary(report)
        logger.info(f"Comparison completed", report_path=report_path)
        print("\n" + summary_text)
        
        return report
    
    async def run_evaluation(self,
                           subset: str = "lite",
                           max_tasks: int = 20,
                           evaluator_types: Optional[List[EvaluatorType]] = None,
                           max_concurrent: int = 3,
                           filter_repos: Optional[List[str]] = None) -> ComparisonReport:
        """Run complete evaluation pipeline.
        
        Args:
            subset: SWE-bench subset to use ("lite", "verified", "full")
            max_tasks: Maximum number of tasks to evaluate
            evaluator_types: List of evaluators to compare (default: all)
            max_concurrent: Maximum concurrent evaluations
            filter_repos: Optional list of repositories to filter by
            
        Returns:
            Comparison report
        """
        
        if evaluator_types is None:
            evaluator_types = [EvaluatorType.CLAUDE, EvaluatorType.CODEX]
        
        logger.info(f"Starting SWE-bench evaluation", 
                   subset=subset,
                   max_tasks=max_tasks,
                   evaluator_types=[e.value for e in evaluator_types],
                   max_concurrent=max_concurrent)
        
        # Load tasks
        tasks = await self.task_loader.load_tasks(
            subset=subset,
            max_tasks=max_tasks,
            filter_repos=filter_repos
        )
        
        if not tasks:
            raise Exception("No tasks loaded")
        
        # Log task statistics
        stats = self.task_loader.get_task_statistics(tasks)
        logger.info(f"Task statistics", **stats)
        
        # Run comparison
        report = await self.compare_evaluators(
            tasks, evaluator_types, max_concurrent
        )
        
        # Log final budget usage
        budget_usage = budget_tracker.get_budget_usage_percentage()
        cost_breakdown = budget_tracker.get_cost_breakdown()
        
        logger.info(f"Evaluation completed", 
                   budget_usage_percent=budget_usage,
                   total_cost=budget_tracker.current_cost,
                   cost_breakdown=cost_breakdown)
        
        if budget_usage > 90:
            eval_logger.budget_warning(
                budget_tracker.current_cost,
                budget_tracker.max_budget
            )
        
        return report
