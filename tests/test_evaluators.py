"""Tests for AI evaluators."""

import pytest
import async<PERSON>
from unittest.mock import Mock, patch, AsyncMock

from src.evaluators import <PERSON><PERSON><PERSON><PERSON><PERSON>, CodexEvaluator, CodeGenEvaluator
from src.models import SW<PERSON>BenchTask, EvaluatorType, TaskStatus


@pytest.fixture
def sample_task():
    """Create a sample SWE-bench task for testing."""
    return SWEBenchTask(
        instance_id="test_task_1",
        repo="test/repo",
        base_commit="abc123",
        problem_statement="Fix the bug in the function",
        hints_text="Look at the error handling",
        created_at="2024-01-01",
        version="1.0",
        FAIL_TO_PASS=["test_function_fix"],
        PASS_TO_PASS=["test_existing_functionality"]
    )


class TestClaudeEvaluator:
    """Test Claude evaluator."""
    
    @pytest.fixture
    def claude_evaluator(self):
        """Create Claude evaluator instance."""
        with patch('src.evaluators.claude_evaluator.anthropic.Anthropic'):
            return ClaudeEvaluator()
    
    def test_initialization(self, claude_evaluator):
        """Test Claude evaluator initialization."""
        assert claude_evaluator.evaluator_type == EvaluatorType.CLAUDE
        assert claude_evaluator.model == "claude-3-5-sonnet-20241022"
    
    def test_rate_limit(self, claude_evaluator):
        """Test rate limit configuration."""
        assert claude_evaluator.get_rate_limit() > 0
    
    def test_cost_estimation(self, claude_evaluator, sample_task):
        """Test cost estimation."""
        cost = claude_evaluator.estimate_cost(sample_task)
        assert cost > 0
        assert cost < 1.0  # Should be reasonable for a single task
    
    @pytest.mark.asyncio
    async def test_generate_solution_success(self, claude_evaluator, sample_task):
        """Test successful solution generation."""
        # Mock the API response
        mock_response = Mock()
        mock_response.content = [Mock()]
        mock_response.content[0].text = """
EXPLANATION:
This fixes the bug by adding proper error handling.

CODE:
def fixed_function():
    try:
        return process_data()
    except ValueError:
        return None

FILES_MODIFIED:
src/main.py
"""
        
        with patch.object(claude_evaluator, '_make_api_call', return_value=mock_response.content[0].text):
            solution = await claude_evaluator.generate_solution(sample_task)
            
            assert solution.code
            assert solution.explanation
            assert "src/main.py" in solution.files_modified
    
    @pytest.mark.asyncio
    async def test_generate_solution_api_error(self, claude_evaluator, sample_task):
        """Test solution generation with API error."""
        with patch.object(claude_evaluator, '_make_api_call', side_effect=Exception("API Error")):
            with pytest.raises(Exception, match="Claude API call failed"):
                await claude_evaluator.generate_solution(sample_task)


class TestCodexEvaluator:
    """Test Codex evaluator."""
    
    @pytest.fixture
    def codex_evaluator(self):
        """Create Codex evaluator instance."""
        return CodexEvaluator()
    
    def test_initialization(self, codex_evaluator):
        """Test Codex evaluator initialization."""
        assert codex_evaluator.evaluator_type == EvaluatorType.CODEX
        assert codex_evaluator.cli_command == "codex"
    
    def test_cost_estimation(self, codex_evaluator, sample_task):
        """Test cost estimation."""
        cost = codex_evaluator.estimate_cost(sample_task)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_check_codex_availability_success(self, codex_evaluator):
        """Test Codex CLI availability check."""
        mock_process = Mock()
        mock_process.returncode = 0
        mock_process.communicate = AsyncMock(return_value=(b"", b""))
        
        with patch('asyncio.create_subprocess_exec', return_value=mock_process):
            # Should not raise exception
            await codex_evaluator._check_codex_availability()
    
    @pytest.mark.asyncio
    async def test_check_codex_availability_not_found(self, codex_evaluator):
        """Test Codex CLI not found."""
        with patch('asyncio.create_subprocess_exec', side_effect=FileNotFoundError()):
            with pytest.raises(Exception, match="Codex CLI not installed"):
                await codex_evaluator._check_codex_availability()


class TestCodeGenEvaluator:
    """Test CodeGen evaluator."""
    
    @pytest.fixture
    def codegen_evaluator(self):
        """Create CodeGen evaluator instance."""
        return CodeGenEvaluator()
    
    def test_initialization(self, codegen_evaluator):
        """Test CodeGen evaluator initialization."""
        assert codegen_evaluator.evaluator_type == EvaluatorType.CODEGEN
        assert codegen_evaluator.api_endpoint
    
    def test_cost_estimation(self, codegen_evaluator, sample_task):
        """Test cost estimation."""
        cost = codegen_evaluator.estimate_cost(sample_task)
        assert cost > 0
    
    def test_create_request_payload(self, codegen_evaluator, sample_task):
        """Test request payload creation."""
        payload = codegen_evaluator._create_request_payload(sample_task)
        
        assert "prompt" in payload
        assert sample_task.problem_statement in payload["prompt"]
        assert payload["max_tokens"] > 0
        assert payload["temperature"] >= 0
    
    def test_parse_response_openai_style(self, codegen_evaluator):
        """Test parsing OpenAI-style response."""
        response_data = {
            "choices": [
                {
                    "text": "def solution():\n    return 'fixed'"
                }
            ]
        }
        
        solution = codegen_evaluator._parse_response(response_data)
        assert solution.code
        assert "def solution" in solution.code
    
    def test_parse_response_direct_code(self, codegen_evaluator):
        """Test parsing direct code response."""
        response_data = {
            "generated_code": "def solution():\n    return 'fixed'"
        }
        
        solution = codegen_evaluator._parse_response(response_data)
        assert solution.code
        assert "def solution" in solution.code


@pytest.mark.asyncio
async def test_base_evaluator_batch_evaluate():
    """Test batch evaluation functionality."""
    from src.evaluators.base_evaluator import BaseEvaluator
    from src.models import CodeSolution
    
    class MockEvaluator(BaseEvaluator):
        def __init__(self):
            super().__init__(EvaluatorType.CLAUDE)
        
        async def generate_solution(self, task):
            return CodeSolution(
                code="def mock_solution(): pass",
                explanation="Mock solution"
            )
        
        def estimate_cost(self, task):
            return 0.01
        
        def get_rate_limit(self):
            return 60
    
    evaluator = MockEvaluator()
    tasks = [
        SWEBenchTask(
            instance_id=f"task_{i}",
            repo="test/repo",
            base_commit="abc123",
            problem_statement=f"Problem {i}",
            hints_text="",
            created_at="2024-01-01",
            version="1.0"
        )
        for i in range(3)
    ]
    
    results = await evaluator.batch_evaluate(tasks, max_concurrent=2)
    
    assert len(results) == 3
    for result in results:
        assert result.status == TaskStatus.COMPLETED
        assert result.solution is not None


if __name__ == "__main__":
    pytest.main([__file__])
