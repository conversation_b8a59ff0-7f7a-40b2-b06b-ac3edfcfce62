"""Tests for SWE-bench integration."""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from src.swe_bench import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, TestRunner, ResultValidator
from src.models import SWEBenchTask, CodeSolution, EvaluationResult, EvaluatorType, TaskStatus


@pytest.fixture
def sample_swe_bench_data():
    """Sample SWE-bench data for testing."""
    return [
        {
            "instance_id": "test_task_1",
            "repo": "test/repo",
            "base_commit": "abc123",
            "problem_statement": "Fix the bug in function X",
            "hints_text": "Check error handling",
            "created_at": "2024-01-01",
            "version": "1.0",
            "FAIL_TO_PASS": ["test_function_x"],
            "PASS_TO_PASS": ["test_existing_feature"]
        },
        {
            "instance_id": "test_task_2", 
            "repo": "test/repo2",
            "base_commit": "def456",
            "problem_statement": "Improve performance of function Y",
            "hints_text": "",
            "created_at": "2024-01-02",
            "version": "1.0",
            "FAIL_TO_PASS": ["test_performance"],
            "PASS_TO_PASS": []
        }
    ]


class TestSWEBenchLoader:
    """Test SWE-bench task loader."""
    
    @pytest.fixture
    def loader(self):
        """Create SWE-bench loader instance."""
        return SWEBenchLoader()
    
    def test_initialization(self, loader):
        """Test loader initialization."""
        assert loader.dataset_path.exists()
        assert loader.cache_file.name == "swe_bench_cache.json"
    
    def test_parse_task_data(self, loader, sample_swe_bench_data):
        """Test parsing task data."""
        task_data = sample_swe_bench_data[0]
        task = loader._parse_task_data(task_data)
        
        assert isinstance(task, SWEBenchTask)
        assert task.instance_id == "test_task_1"
        assert task.repo == "test/repo"
        assert task.problem_statement == "Fix the bug in function X"
        assert "test_function_x" in task.FAIL_TO_PASS
    
    def test_parse_task_data_missing_required_field(self, loader):
        """Test parsing with missing required field."""
        invalid_data = {"repo": "test/repo"}  # Missing instance_id
        
        with pytest.raises(ValueError, match="Missing instance_id"):
            loader._parse_task_data(invalid_data)
    
    @pytest.mark.asyncio
    async def test_load_from_cache(self, loader, sample_swe_bench_data):
        """Test loading tasks from cache."""
        # Create temporary cache file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            cache_data = {"lite": sample_swe_bench_data}
            json.dump(cache_data, f)
            cache_file = Path(f.name)
        
        # Mock the cache file path
        loader.cache_file = cache_file
        
        try:
            tasks = await loader._load_from_cache("lite")
            assert len(tasks) == 2
            assert all(isinstance(task, SWEBenchTask) for task in tasks)
        finally:
            cache_file.unlink()
    
    @pytest.mark.asyncio
    async def test_download_dataset(self, loader, sample_swe_bench_data):
        """Test downloading dataset."""
        mock_response = Mock()
        mock_response.json.return_value = sample_swe_bench_data
        mock_response.raise_for_status.return_value = None
        
        with patch('requests.get', return_value=mock_response):
            tasks = await loader._download_dataset("lite")
            
            assert len(tasks) == 2
            assert all(isinstance(task, SWEBenchTask) for task in tasks)
    
    def test_get_task_statistics(self, loader, sample_swe_bench_data):
        """Test task statistics calculation."""
        tasks = [loader._parse_task_data(data) for data in sample_swe_bench_data]
        stats = loader.get_task_statistics(tasks)
        
        assert stats["total_tasks"] == 2
        assert stats["unique_repos"] == 2
        assert "repo_distribution" in stats
        assert stats["tasks_with_tests"] == 2


class TestTestRunner:
    """Test test runner."""
    
    @pytest.fixture
    def test_runner(self):
        """Create test runner instance."""
        with patch('src.swe_bench.test_runner.E2BSandboxManager'):
            return TestRunner(use_e2b=True)
    
    @pytest.fixture
    def sample_task(self):
        """Create sample task."""
        return SWEBenchTask(
            instance_id="test_task",
            repo="test/repo",
            base_commit="abc123",
            problem_statement="Fix bug",
            hints_text="",
            created_at="2024-01-01",
            version="1.0",
            FAIL_TO_PASS=["test_bug_fix"],
            PASS_TO_PASS=["test_existing"]
        )
    
    @pytest.fixture
    def sample_solution(self):
        """Create sample solution."""
        return CodeSolution(
            code="def fixed_function(): return True",
            explanation="Fixed the bug",
            files_modified=["src/main.py"]
        )
    
    def test_initialization(self, test_runner):
        """Test test runner initialization."""
        assert test_runner.use_e2b is True
        assert test_runner.sandbox_manager is not None
    
    def test_is_test_result_correct_fail_to_pass(self, test_runner, sample_task):
        """Test correctness evaluation for FAIL_TO_PASS tests."""
        from src.models import TestResult
        
        # Test should fail in baseline and pass after solution
        baseline_result = TestResult(test_name="test_bug_fix", status="FAILED", output="")
        solution_result = TestResult(test_name="test_bug_fix", status="PASSED", output="")
        
        is_correct = test_runner._is_test_result_correct(
            sample_task, "test_bug_fix", baseline_result, solution_result
        )
        
        assert is_correct is True
    
    def test_is_test_result_correct_pass_to_pass(self, test_runner, sample_task):
        """Test correctness evaluation for PASS_TO_PASS tests."""
        from src.models import TestResult
        
        # Test should pass in both baseline and after solution
        baseline_result = TestResult(test_name="test_existing", status="PASSED", output="")
        solution_result = TestResult(test_name="test_existing", status="PASSED", output="")
        
        is_correct = test_runner._is_test_result_correct(
            sample_task, "test_existing", baseline_result, solution_result
        )
        
        assert is_correct is True
    
    @pytest.mark.asyncio
    async def test_validate_solution(self, test_runner, sample_task, sample_solution):
        """Test solution validation."""
        from src.models import TestResult
        
        # Mock test results
        mock_test_results = [
            TestResult(test_name="test_bug_fix", status="PASSED", output=""),
            TestResult(test_name="test_existing", status="PASSED", output="")
        ]
        
        with patch.object(test_runner, 'run_task_tests', return_value=mock_test_results):
            validation_result = await test_runner.validate_solution(sample_task, sample_solution)
            
            assert validation_result["overall_success"] is True
            assert validation_result["total_tests"] == 2
            assert validation_result["passed_tests"] == 2
            assert validation_result["success_rate"] == 1.0


class TestResultValidator:
    """Test result validator."""
    
    @pytest.fixture
    def validator(self):
        """Create result validator instance."""
        return ResultValidator()
    
    @pytest.fixture
    def sample_result(self):
        """Create sample evaluation result."""
        from src.models import TestResult
        
        return EvaluationResult(
            task_id="test_task",
            evaluator_type=EvaluatorType.CLAUDE,
            status=TaskStatus.COMPLETED,
            solution=CodeSolution(
                code="def solution(): pass",
                explanation="Test solution"
            ),
            test_results=[
                TestResult(
                    test_name="test_1",
                    status="PASSED",
                    output="[EVALUATION] Test correctness: CORRECT"
                ),
                TestResult(
                    test_name="test_2", 
                    status="FAILED",
                    output="[EVALUATION] Test correctness: INCORRECT"
                )
            ],
            tests_passed=1,
            tests_failed=1,
            execution_time=10.0,
            api_cost=0.05
        )
    
    def test_validate_evaluation_result(self, validator, sample_result):
        """Test evaluation result validation."""
        validation_report = validator.validate_evaluation_result(sample_result)
        
        assert validation_report["is_valid"] is True
        assert validation_report["task_id"] == "test_task"
        assert "metrics" in validation_report
        assert validation_report["metrics"]["total_tests"] == 2
        assert validation_report["metrics"]["passed_tests"] == 1
    
    def test_calculate_swe_bench_metrics(self, validator, sample_result):
        """Test SWE-bench metrics calculation."""
        metrics = validator._calculate_swe_bench_metrics(sample_result)
        
        assert metrics["total_tests"] == 2
        assert metrics["passed_tests"] == 1
        assert metrics["failed_tests"] == 1
        assert metrics["correct_tests"] == 1
        assert metrics["pass_rate"] == 0.5
        assert metrics["correctness_rate"] == 0.5
        assert metrics["swe_bench_success"] is False  # Not all tests correct
    
    def test_create_evaluation_summary(self, validator):
        """Test evaluation summary creation."""
        from src.models import TestResult
        
        results = [
            EvaluationResult(
                task_id="task_1",
                evaluator_type=EvaluatorType.CLAUDE,
                status=TaskStatus.COMPLETED,
                test_results=[TestResult(test_name="test", status="PASSED", output="")],
                tests_passed=1,
                tests_failed=0,
                execution_time=5.0,
                api_cost=0.02
            ),
            EvaluationResult(
                task_id="task_2",
                evaluator_type=EvaluatorType.CLAUDE,
                status=TaskStatus.FAILED,
                error_message="API error"
            )
        ]
        
        summary = validator.create_evaluation_summary(results, EvaluatorType.CLAUDE)
        
        assert summary.evaluator_type == EvaluatorType.CLAUDE
        assert summary.total_tasks == 2
        assert summary.completed_tasks == 1
        assert summary.failed_tasks == 1
        assert summary.total_tests_passed == 1
        assert summary.total_api_cost == 0.02
    
    def test_save_and_load_results(self, validator, sample_result):
        """Test saving and loading results."""
        results = [sample_result]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            validator.results_path = Path(temp_dir)
            
            # Save results
            filepath = validator.save_results(results, "test_results.json")
            assert Path(filepath).exists()
            
            # Load results
            loaded_results = validator.load_results(filepath)
            assert len(loaded_results) == 1
            assert loaded_results[0].task_id == sample_result.task_id


if __name__ == "__main__":
    pytest.main([__file__])
