"""Modal deployment for parallel SWE-bench evaluation."""

import modal
import asyncio
from typing import List, Dict, Any
import json
import os

# Create Modal app
app = modal.App("swe-bench-evaluator")

# Define the container image with all dependencies
image = (
    modal.Image.debian_slim()
    .pip_install([
        "anthropic>=0.40.0",
        "openai>=1.50.0",
        "requests>=2.31.0",
        "aiohttp>=3.9.0",
        "asyncio-throttle>=1.0.2",
        "e2b>=0.17.0",
        "docker>=7.0.0",
        "pandas>=2.1.0",
        "numpy>=1.24.0",
        "pydantic>=2.5.0",
        "click>=8.1.0",
        "rich>=13.7.0",
        "python-dotenv>=1.0.0",
        "pyyaml>=6.0.1",
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "structlog>=23.2.0"
    ])
    .apt_install(["git"])
)

# Define secrets for API keys
secrets = [
    modal.Secret.from_name("anthropic-api-key"),
    modal.Secret.from_name("openai-api-key"),
    modal.Secret.from_name("e2b-api-key")
]


@app.function(
    image=image,
    secrets=secrets,
    timeout=1800,  # 30 minutes timeout
    memory=2048,   # 2GB memory
    cpu=2.0        # 2 CPU cores
)
async def evaluate_single_task_modal(task_data: Dict[str, Any], 
                                   evaluator_type: str) -> Dict[str, Any]:
    """Evaluate a single task in Modal."""
    
    # Import here to avoid issues with Modal
    import sys
    sys.path.append('/root')
    
    from src.models import SWEBenchTask, EvaluatorType
    from src.evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
    from src.swe_bench import TestRunner
    from src.config import config
    
    # Override config with environment variables
    config.anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")
    config.openai_api_key = os.environ.get("OPENAI_API_KEY")
    config.e2b_api_key = os.environ.get("E2B_API_KEY")
    
    try:
        # Parse task data
        task = SWEBenchTask(**task_data)
        evaluator_type_enum = EvaluatorType(evaluator_type)
        
        # Create evaluator
        evaluators = {
            EvaluatorType.CLAUDE: ClaudeEvaluator(),
            EvaluatorType.CODEX: CodexEvaluator(),
            EvaluatorType.CODEGEN: CodeGenEvaluator()
        }
        
        evaluator = evaluators[evaluator_type_enum]
        
        # Evaluate task
        result = await evaluator.evaluate_task(task)
        
        # If solution was generated, run tests
        if result.solution:
            test_runner = TestRunner(use_e2b=True)
            validation_results = await test_runner.validate_solution(
                task, result.solution
            )
            
            # Update result with test information
            result.test_results = validation_results.get("test_results", [])
            result.tests_passed = validation_results.get("passed_tests", 0)
            result.tests_failed = validation_results.get("failed_tests", 0)
        
        # Convert result to dictionary for serialization
        result_dict = result.dict()
        
        # Convert datetime objects to strings
        if result_dict.get('started_at'):
            result_dict['started_at'] = result_dict['started_at'].isoformat()
        if result_dict.get('completed_at'):
            result_dict['completed_at'] = result_dict['completed_at'].isoformat()
        
        return result_dict
        
    except Exception as e:
        # Return error result
        return {
            "task_id": task_data.get("instance_id", "unknown"),
            "evaluator_type": evaluator_type,
            "status": "failed",
            "error_message": str(e)
        }


@app.function(
    image=image,
    secrets=secrets,
    timeout=3600,  # 1 hour timeout
    memory=1024    # 1GB memory
)
async def evaluate_batch_modal(tasks_data: List[Dict[str, Any]], 
                             evaluator_type: str,
                             max_concurrent: int = 5) -> List[Dict[str, Any]]:
    """Evaluate a batch of tasks in Modal with concurrency control."""
    
    # Create semaphore for concurrency control
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def evaluate_with_semaphore(task_data):
        async with semaphore:
            return await evaluate_single_task_modal.remote(task_data, evaluator_type)
    
    # Execute all tasks concurrently
    results = await asyncio.gather(
        *[evaluate_with_semaphore(task_data) for task_data in tasks_data],
        return_exceptions=True
    )
    
    # Handle exceptions
    final_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            error_result = {
                "task_id": tasks_data[i].get("instance_id", "unknown"),
                "evaluator_type": evaluator_type,
                "status": "failed",
                "error_message": str(result)
            }
            final_results.append(error_result)
        else:
            final_results.append(result)
    
    return final_results


@app.function(
    image=image,
    secrets=secrets,
    timeout=7200,  # 2 hours timeout
    memory=2048    # 2GB memory
)
async def run_full_evaluation_modal(subset: str = "lite",
                                  max_tasks: int = 20,
                                  evaluator_types: List[str] = None,
                                  max_concurrent: int = 3) -> Dict[str, Any]:
    """Run full evaluation in Modal."""
    
    if evaluator_types is None:
        evaluator_types = ["claude", "codex"]
    
    # Import here to avoid Modal issues
    import sys
    sys.path.append('/root')
    
    from src.swe_bench import SWEBenchLoader
    from src.models import EvaluatorType
    
    # Override config
    from src.config import config
    config.anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")
    config.openai_api_key = os.environ.get("OPENAI_API_KEY")
    config.e2b_api_key = os.environ.get("E2B_API_KEY")
    
    try:
        # Load tasks
        loader = SWEBenchLoader()
        tasks = await loader.load_tasks(subset=subset, max_tasks=max_tasks)
        
        if not tasks:
            return {"error": "No tasks loaded"}
        
        # Convert tasks to serializable format
        tasks_data = [task.dict() for task in tasks]
        
        # Run evaluation for each evaluator type
        all_results = {}
        
        for evaluator_type in evaluator_types:
            print(f"Evaluating with {evaluator_type}...")
            
            # Split tasks into batches for better resource management
            batch_size = 10
            batches = [tasks_data[i:i+batch_size] for i in range(0, len(tasks_data), batch_size)]
            
            evaluator_results = []
            for batch in batches:
                batch_results = await evaluate_batch_modal.remote(
                    batch, evaluator_type, max_concurrent
                )
                evaluator_results.extend(batch_results)
            
            all_results[evaluator_type] = evaluator_results
        
        return {
            "success": True,
            "results": all_results,
            "total_tasks": len(tasks),
            "evaluator_types": evaluator_types
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


# Local function to trigger Modal evaluation
@app.local_entrypoint()
def main(subset: str = "lite", 
         max_tasks: int = 20, 
         evaluators: str = "claude,codex",
         workers: int = 3):
    """Local entrypoint to run evaluation on Modal."""
    
    evaluator_list = [e.strip() for e in evaluators.split(',')]
    
    print(f"Starting Modal evaluation...")
    print(f"Subset: {subset}")
    print(f"Max tasks: {max_tasks}")
    print(f"Evaluators: {evaluator_list}")
    print(f"Workers: {workers}")
    
    # Run evaluation
    result = run_full_evaluation_modal.remote(
        subset=subset,
        max_tasks=max_tasks,
        evaluator_types=evaluator_list,
        max_concurrent=workers
    )
    
    if result.get("success"):
        print(f"\n✅ Evaluation completed successfully!")
        print(f"Total tasks: {result['total_tasks']}")
        
        # Save results
        import json
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"modal_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"Results saved to: {filename}")
        
        # Print summary
        for evaluator_type, results in result["results"].items():
            completed = sum(1 for r in results if r.get("status") == "completed")
            failed = sum(1 for r in results if r.get("status") == "failed")
            total_cost = sum(r.get("api_cost", 0) for r in results)
            
            print(f"\n{evaluator_type.title()} Results:")
            print(f"  Completed: {completed}/{len(results)}")
            print(f"  Failed: {failed}")
            print(f"  Total Cost: ${total_cost:.2f}")
    
    else:
        print(f"❌ Evaluation failed: {result.get('error')}")


if __name__ == "__main__":
    # Example usage:
    # modal run modal_deployment.py --subset lite --max-tasks 10 --evaluators claude,codex --workers 3
    import sys
    
    if len(sys.argv) > 1:
        # Parse command line arguments
        args = {}
        for i in range(1, len(sys.argv), 2):
            if i + 1 < len(sys.argv):
                key = sys.argv[i].lstrip('-')
                value = sys.argv[i + 1]
                
                if key in ['max-tasks', 'workers']:
                    value = int(value)
                
                args[key.replace('-', '_')] = value
        
        main(**args)
    else:
        main()
