This file is a merged representation of the entire codebase, combining all repository files into a single document.
Generated by Repomix on: 2025-06-23T03:24:59.911Z

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Repository structure
4. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's
  configuration.
- Binary files are not included in this packed representation. Please refer to
  the Repository Structure section for a complete list of file paths, including
  binary files.

Additional Info:
----------------

For more information about Repomix, visit: https://github.com/yamadashy/repomix

================================================================
Repository Structure
================================================================
src/
  evaluators/
    __init__.py
    base_evaluator.py
    claude_evaluator.py
    codegen_evaluator.py
    codex_evaluator.py
  sandbox/
    __init__.py
    docker_manager.py
    e2b_manager.py
  swe_bench/
    __init__.py
    result_validator.py
    task_loader.py
    test_runner.py
  utils/
    __init__.py
    logger.py
    rate_limiter.py
  __init__.py
  config.py
  evaluator.py
  models.py
tests/
  __init__.py
  test_evaluators.py
  test_swe_bench.py
.env.example
ARCHITECTURE.md
cli.py
context.md
Makefile
modal_deployment.py
pytest.ini
QUICKSTART.md
README.md
requirements.txt
setup.py
test_basic_functionality.py

================================================================
Repository Files
================================================================

================
File: src/evaluators/__init__.py
================
"""AI evaluator modules."""

from .base_evaluator import BaseEvaluator
from .claude_evaluator import ClaudeEvaluator
from .codex_evaluator import CodexEvaluator
from .codegen_evaluator import CodeGenEvaluator

__all__ = [
    "BaseEvaluator",
    "ClaudeEvaluator", 
    "CodexEvaluator",
    "CodeGenEvaluator"
]

================
File: src/evaluators/base_evaluator.py
================
"""Base evaluator class for AI coding tools."""

import asyncio
import time
from abc import ABC, abstractmethod
from typing import Optional, Dict, Any

from ..models import (
    SWEBenchTask, 
    CodeSolution, 
    EvaluationResult, 
    TaskStatus, 
    EvaluatorType
)
from ..utils import eval_logger, rate_limiter, budget_tracker
from ..config import config


class BaseEvaluator(ABC):
    """Base class for AI evaluators."""
    
    def __init__(self, evaluator_type: EvaluatorType):
        self.evaluator_type = evaluator_type
        self.total_cost = 0.0
        self.requests_made = 0
    
    @abstractmethod
    async def generate_solution(self, task: SWEBenchTask) -> CodeSolution:
        """Generate a code solution for the given task.
        
        Args:
            task: SWE-bench task to solve
            
        Returns:
            Generated code solution
            
        Raises:
            Exception: If solution generation fails
        """
        pass
    
    @abstractmethod
    def estimate_cost(self, task: SWEBenchTask) -> float:
        """Estimate the cost of solving this task.
        
        Args:
            task: SWE-bench task to estimate cost for
            
        Returns:
            Estimated cost in USD
        """
        pass
    
    @abstractmethod
    def get_rate_limit(self) -> int:
        """Get requests per minute rate limit for this evaluator.
        
        Returns:
            Requests per minute limit
        """
        pass
    
    async def evaluate_task(self, task: SWEBenchTask) -> EvaluationResult:
        """Evaluate a single SWE-bench task.
        
        Args:
            task: Task to evaluate
            
        Returns:
            Evaluation result
        """
        result = EvaluationResult(
            task_id=task.instance_id,
            evaluator_type=self.evaluator_type,
            status=TaskStatus.PENDING
        )
        
        try:
            # Check budget before starting
            estimated_cost = self.estimate_cost(task)
            if not budget_tracker.can_afford(estimated_cost):
                result.status = TaskStatus.FAILED
                result.error_message = f"Insufficient budget. Need ${estimated_cost:.2f}, have ${budget_tracker.get_remaining_budget():.2f}"
                return result
            
            # Log task start
            eval_logger.task_started(task.instance_id, self.evaluator_type.value)
            result.status = TaskStatus.RUNNING
            
            # Rate limiting
            await rate_limiter.acquire(
                self.evaluator_type.value, 
                self.get_rate_limit()
            )
            
            # Generate solution with timeout
            start_time = time.time()
            try:
                solution = await asyncio.wait_for(
                    self.generate_solution(task),
                    timeout=config.task_timeout_seconds
                )
                result.solution = solution
            except asyncio.TimeoutError:
                result.status = TaskStatus.TIMEOUT
                result.error_message = f"Task timed out after {config.task_timeout_seconds} seconds"
                return result
            
            result.execution_time = time.time() - start_time
            result.status = TaskStatus.COMPLETED
            
            # Update costs
            actual_cost = self._calculate_actual_cost(task, solution)
            result.api_cost = actual_cost
            self.total_cost += actual_cost
            budget_tracker.add_cost(self.evaluator_type.value, actual_cost)
            
            # Log completion
            eval_logger.task_completed(
                task.instance_id,
                self.evaluator_type.value,
                0.0,  # Will be updated after test execution
                result.execution_time,
                actual_cost
            )
            
        except Exception as e:
            result.status = TaskStatus.FAILED
            result.error_message = str(e)
            eval_logger.task_failed(
                task.instance_id,
                self.evaluator_type.value,
                str(e)
            )
        
        return result
    
    def _calculate_actual_cost(self, task: SWEBenchTask, solution: CodeSolution) -> float:
        """Calculate actual cost after solution generation.
        
        This is a placeholder - subclasses should override with actual cost calculation.
        """
        return self.estimate_cost(task)
    
    async def batch_evaluate(self, tasks: list[SWEBenchTask], 
                           max_concurrent: int = 3) -> list[EvaluationResult]:
        """Evaluate multiple tasks concurrently.
        
        Args:
            tasks: List of tasks to evaluate
            max_concurrent: Maximum concurrent evaluations
            
        Returns:
            List of evaluation results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def evaluate_with_semaphore(task: SWEBenchTask) -> EvaluationResult:
            async with semaphore:
                return await self.evaluate_task(task)
        
        # Create tasks for concurrent execution
        evaluation_tasks = [
            evaluate_with_semaphore(task) for task in tasks
        ]
        
        # Execute all tasks concurrently
        results = await asyncio.gather(*evaluation_tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                # Create failed result for exceptions
                failed_result = EvaluationResult(
                    task_id=tasks[i].instance_id,
                    evaluator_type=self.evaluator_type,
                    status=TaskStatus.FAILED,
                    error_message=str(result)
                )
                final_results.append(failed_result)
            else:
                final_results.append(result)
        
        return final_results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get evaluator statistics.
        
        Returns:
            Dictionary of statistics
        """
        return {
            "evaluator_type": self.evaluator_type.value,
            "total_cost": self.total_cost,
            "requests_made": self.requests_made,
            "average_cost_per_request": self.total_cost / max(1, self.requests_made)
        }

================
File: src/evaluators/claude_evaluator.py
================
"""Claude evaluator implementation."""

import anthropic
from typing import Optional

from .base_evaluator import BaseEvaluator
from ..models import SWEBenchTask, CodeSolution, EvaluatorType
from ..config import config
from ..utils import eval_logger


class ClaudeEvaluator(BaseEvaluator):
    """Evaluator using Claude API."""
    
    def __init__(self):
        super().__init__(EvaluatorType.CLAUDE)
        self.client = anthropic.Anthropic(api_key=config.anthropic_api_key)
        self.model = "claude-3-5-sonnet-20241022"  # Latest Claude model
    
    def get_rate_limit(self) -> int:
        """Get Claude API rate limit."""
        return config.claude_requests_per_minute
    
    def estimate_cost(self, task: SWEBenchTask) -> float:
        """Estimate cost for Claude API call.
        
        Claude pricing (approximate):
        - Input: $3 per million tokens
        - Output: $15 per million tokens
        """
        # Rough estimation based on task complexity
        problem_length = len(task.problem_statement)
        hints_length = len(task.hints_text)
        
        # Estimate input tokens (rough approximation: 4 chars per token)
        estimated_input_tokens = (problem_length + hints_length + 2000) / 4  # +2000 for system prompt
        estimated_output_tokens = 1000  # Assume ~1000 tokens for code solution
        
        input_cost = (estimated_input_tokens / 1_000_000) * 3.0
        output_cost = (estimated_output_tokens / 1_000_000) * 15.0
        
        return input_cost + output_cost
    
    async def generate_solution(self, task: SWEBenchTask) -> CodeSolution:
        """Generate solution using Claude API."""
        
        # Construct the prompt
        system_prompt = """You are an expert software engineer. You will be given a GitHub issue description and need to implement a solution.

Your task:
1. Analyze the problem statement carefully
2. Understand what changes are needed
3. Generate the necessary code changes
4. Provide a clear explanation of your solution

Focus on:
- Writing clean, maintainable code
- Following the existing codebase patterns
- Ensuring your solution addresses the specific issue
- Making minimal necessary changes

Return your response in this format:
EXPLANATION:
[Your explanation of the solution]

CODE:
[Your code implementation]

FILES_MODIFIED:
[List of files that need to be modified, one per line]
"""
        
        user_prompt = f"""
Repository: {task.repo}
Issue ID: {task.instance_id}

Problem Statement:
{task.problem_statement}

Additional Hints:
{task.hints_text}

Please provide a solution for this issue.
"""
        
        try:
            # Make API call to Claude
            response = await self._make_api_call(system_prompt, user_prompt)
            
            # Parse the response
            solution = self._parse_response(response)
            
            # Update request count
            self.requests_made += 1
            
            # Log API call
            eval_logger.api_call(
                self.evaluator_type.value,
                self.estimate_cost(task),
                tokens_used=0  # Claude doesn't provide token count in response
            )
            
            return solution
            
        except Exception as e:
            raise Exception(f"Claude API call failed: {str(e)}")
    
    async def _make_api_call(self, system_prompt: str, user_prompt: str) -> str:
        """Make API call to Claude."""
        try:
            message = await self.client.messages.create(
                model=self.model,
                max_tokens=4000,
                temperature=0.1,  # Low temperature for consistent code generation
                system=system_prompt,
                messages=[
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            )
            
            return message.content[0].text
            
        except anthropic.APIError as e:
            raise Exception(f"Claude API error: {str(e)}")
        except Exception as e:
            raise Exception(f"Unexpected error calling Claude API: {str(e)}")
    
    def _parse_response(self, response: str) -> CodeSolution:
        """Parse Claude's response into a CodeSolution."""
        
        # Initialize default values
        explanation = ""
        code = ""
        files_modified = []
        
        # Split response into sections
        sections = response.split('\n')
        current_section = None
        current_content = []
        
        for line in sections:
            line = line.strip()
            
            if line.startswith('EXPLANATION:'):
                if current_section and current_content:
                    self._process_section(current_section, current_content, 
                                        explanation, code, files_modified)
                current_section = 'explanation'
                current_content = []
            elif line.startswith('CODE:'):
                if current_section and current_content:
                    explanation = self._process_section(current_section, current_content, 
                                                      explanation, code, files_modified)
                current_section = 'code'
                current_content = []
            elif line.startswith('FILES_MODIFIED:'):
                if current_section and current_content:
                    code = self._process_section(current_section, current_content, 
                                                explanation, code, files_modified)
                current_section = 'files'
                current_content = []
            else:
                if current_section:
                    current_content.append(line)
        
        # Process the last section
        if current_section and current_content:
            if current_section == 'explanation':
                explanation = '\n'.join(current_content).strip()
            elif current_section == 'code':
                code = '\n'.join(current_content).strip()
            elif current_section == 'files':
                files_modified = [f.strip() for f in current_content if f.strip()]
        
        # If parsing failed, use the entire response as code
        if not code and not explanation:
            code = response
            explanation = "Generated solution (parsing failed)"
        
        return CodeSolution(
            code=code,
            explanation=explanation,
            files_modified=files_modified,
            confidence_score=0.8  # Default confidence for Claude
        )
    
    def _process_section(self, section_type: str, content: list, 
                        explanation: str, code: str, files_modified: list) -> str:
        """Process a section of the response."""
        content_str = '\n'.join(content).strip()
        
        if section_type == 'explanation':
            return content_str
        elif section_type == 'code':
            return content_str
        elif section_type == 'files':
            files_modified.extend([f.strip() for f in content if f.strip()])
        
        return ""
    
    def _calculate_actual_cost(self, task: SWEBenchTask, solution: CodeSolution) -> float:
        """Calculate actual cost based on the API response.
        
        For now, we'll use the estimated cost since Claude doesn't provide
        detailed token usage in the response.
        """
        return self.estimate_cost(task)

================
File: src/evaluators/codegen_evaluator.py
================
"""CodeGen evaluator implementation."""

import aiohttp
import json
from typing import Optional, Dict, Any

from .base_evaluator import BaseEvaluator
from ..models import SWEBenchTask, CodeSolution, EvaluatorType
from ..config import config
from ..utils import eval_logger


class CodeGenEvaluator(BaseEvaluator):
    """Evaluator using CodeGen API."""
    
    def __init__(self):
        super().__init__(EvaluatorType.CODEGEN)
        # CodeGen API endpoint (this would need to be configured)
        self.api_endpoint = "https://api.codegen.example.com/v1/generate"  # Placeholder
        self.api_key = config.openai_api_key  # Assuming CodeGen uses OpenAI-style auth
    
    def get_rate_limit(self) -> int:
        """Get CodeGen API rate limit."""
        return 30  # Conservative rate limit for CodeGen
    
    def estimate_cost(self, task: SWEBenchTask) -> float:
        """Estimate cost for CodeGen API call.
        
        CodeGen pricing (estimated):
        - Similar to other code generation APIs
        """
        problem_length = len(task.problem_statement)
        hints_length = len(task.hints_text)
        
        # Estimate tokens
        estimated_input_tokens = (problem_length + hints_length + 1000) / 4
        estimated_output_tokens = 600
        
        # Estimated pricing (adjust based on actual CodeGen pricing)
        input_cost = (estimated_input_tokens / 1_000_000) * 5.0
        output_cost = (estimated_output_tokens / 1_000_000) * 20.0
        
        return input_cost + output_cost
    
    async def generate_solution(self, task: SWEBenchTask) -> CodeSolution:
        """Generate solution using CodeGen API."""
        
        try:
            # Prepare the request payload
            payload = self._create_request_payload(task)
            
            # Make API call
            response_data = await self._make_api_call(payload)
            
            # Parse response
            solution = self._parse_response(response_data)
            
            # Update request count
            self.requests_made += 1
            
            # Log API call
            eval_logger.api_call(
                self.evaluator_type.value,
                self.estimate_cost(task)
            )
            
            return solution
            
        except Exception as e:
            raise Exception(f"CodeGen API call failed: {str(e)}")
    
    def _create_request_payload(self, task: SWEBenchTask) -> Dict[str, Any]:
        """Create request payload for CodeGen API."""
        
        prompt = f"""
You are an expert software engineer. Analyze the following GitHub issue and provide a complete solution.

Repository: {task.repo}
Issue ID: {task.instance_id}

Problem Statement:
{task.problem_statement}

Additional Context:
{task.hints_text}

Please provide:
1. A clear explanation of the solution approach
2. The complete code implementation
3. List of files that need to be modified

Focus on writing clean, maintainable code that follows best practices.
"""
        
        return {
            "prompt": prompt,
            "max_tokens": 2000,
            "temperature": 0.1,
            "model": "codegen-latest",  # Placeholder model name
            "stop": ["<|endoftext|>"],
            "task_type": "code_generation",
            "context": {
                "repo": task.repo,
                "issue_id": task.instance_id,
                "language": "python"  # Default to Python, could be detected
            }
        }
    
    async def _make_api_call(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """Make API call to CodeGen."""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "User-Agent": "AI-Code-Evaluator/1.0"
        }
        
        timeout = aiohttp.ClientTimeout(total=config.task_timeout_seconds)
        
        async with aiohttp.ClientSession(timeout=timeout) as session:
            try:
                async with session.post(
                    self.api_endpoint,
                    json=payload,
                    headers=headers
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        raise Exception(f"API returned status {response.status}: {error_text}")
                    
                    return await response.json()
                    
            except aiohttp.ClientError as e:
                raise Exception(f"HTTP client error: {str(e)}")
            except asyncio.TimeoutError:
                raise Exception(f"API call timed out after {config.task_timeout_seconds} seconds")
    
    def _parse_response(self, response_data: Dict[str, Any]) -> CodeSolution:
        """Parse CodeGen API response."""
        
        # Handle different possible response formats
        if "choices" in response_data:
            # OpenAI-style response
            if not response_data["choices"]:
                raise Exception("No choices in API response")
            
            generated_text = response_data["choices"][0].get("text", "")
            
        elif "generated_code" in response_data:
            # Direct code response
            generated_text = response_data["generated_code"]
            
        elif "output" in response_data:
            # Generic output field
            generated_text = response_data["output"]
            
        else:
            # Fallback: look for any text field
            generated_text = str(response_data)
        
        if not generated_text:
            raise Exception("No generated text found in API response")
        
        # Parse the generated text
        return self._extract_solution_components(generated_text)
    
    def _extract_solution_components(self, generated_text: str) -> CodeSolution:
        """Extract solution components from generated text."""
        
        # Initialize components
        explanation = ""
        code = ""
        files_modified = []
        
        # Try to parse structured response
        lines = generated_text.split('\n')
        current_section = None
        current_content = []
        
        for line in lines:
            line_lower = line.lower().strip()
            
            # Detect section headers
            if any(keyword in line_lower for keyword in ['explanation:', 'approach:', 'solution:']):
                if current_section == 'code':
                    code = '\n'.join(current_content).strip()
                elif current_section == 'explanation':
                    explanation = '\n'.join(current_content).strip()
                
                current_section = 'explanation'
                current_content = []
                
            elif any(keyword in line_lower for keyword in ['code:', 'implementation:', '```']):
                if current_section == 'explanation':
                    explanation = '\n'.join(current_content).strip()
                
                current_section = 'code'
                current_content = []
                
                # Skip the opening ``` line
                if '```' in line:
                    continue
                    
            elif any(keyword in line_lower for keyword in ['files:', 'modified:', 'changes:']):
                if current_section == 'code':
                    code = '\n'.join(current_content).strip()
                elif current_section == 'explanation':
                    explanation = '\n'.join(current_content).strip()
                
                current_section = 'files'
                current_content = []
                
            else:
                if current_section:
                    # Skip closing ``` for code sections
                    if current_section == 'code' and line.strip() == '```':
                        continue
                    current_content.append(line)
        
        # Process the last section
        if current_section == 'code':
            code = '\n'.join(current_content).strip()
        elif current_section == 'explanation':
            explanation = '\n'.join(current_content).strip()
        elif current_section == 'files':
            files_modified = [f.strip() for f in current_content if f.strip()]
        
        # If no structured parsing worked, treat everything as code
        if not code and not explanation:
            code = generated_text.strip()
            explanation = "Generated solution from CodeGen"
        
        # Clean up code (remove common artifacts)
        if code:
            code = self._clean_generated_code(code)
        
        return CodeSolution(
            code=code,
            explanation=explanation or "Solution generated by CodeGen API",
            files_modified=files_modified,
            confidence_score=0.75  # Default confidence for CodeGen
        )
    
    def _clean_generated_code(self, code: str) -> str:
        """Clean up generated code by removing common artifacts."""
        
        # Remove common prefixes/suffixes
        lines = code.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # Skip empty lines at the beginning
            if not cleaned_lines and not line.strip():
                continue
            
            # Skip common artifacts
            if any(artifact in line.lower() for artifact in [
                'here is the', 'here\'s the', 'the solution is',
                'generated by', 'ai assistant'
            ]):
                continue
            
            cleaned_lines.append(line)
        
        # Remove trailing empty lines
        while cleaned_lines and not cleaned_lines[-1].strip():
            cleaned_lines.pop()
        
        return '\n'.join(cleaned_lines)
    
    def _calculate_actual_cost(self, task: SWEBenchTask, solution: CodeSolution) -> float:
        """Calculate actual cost for CodeGen API usage."""
        # Use estimated cost since CodeGen API details are not specified
        return self.estimate_cost(task)

================
File: src/evaluators/codex_evaluator.py
================
"""Codex evaluator implementation."""

import asyncio
import subprocess
import json
import tempfile
import os
from pathlib import Path
from typing import Optional, Dict, Any

from .base_evaluator import BaseEvaluator
from ..models import SWEBenchTask, CodeSolution, EvaluatorType
from ..config import config
from ..utils import eval_logger


class CodexEvaluator(BaseEvaluator):
    """Evaluator using Codex CLI."""
    
    def __init__(self):
        super().__init__(EvaluatorType.CODEX)
        self.cli_command = "codex"  # Assuming codex CLI is installed
    
    def get_rate_limit(self) -> int:
        """Get Codex CLI rate limit."""
        return config.openai_requests_per_minute
    
    def estimate_cost(self, task: SWEBenchTask) -> float:
        """Estimate cost for Codex CLI usage.
        
        Codex pricing is similar to GPT-4:
        - Input: ~$10 per million tokens
        - Output: ~$30 per million tokens
        """
        problem_length = len(task.problem_statement)
        hints_length = len(task.hints_text)
        
        # Estimate tokens (rough approximation: 4 chars per token)
        estimated_input_tokens = (problem_length + hints_length + 1500) / 4
        estimated_output_tokens = 800
        
        input_cost = (estimated_input_tokens / 1_000_000) * 10.0
        output_cost = (estimated_output_tokens / 1_000_000) * 30.0
        
        return input_cost + output_cost
    
    async def generate_solution(self, task: SWEBenchTask) -> CodeSolution:
        """Generate solution using Codex CLI."""
        
        try:
            # Check if Codex CLI is available
            await self._check_codex_availability()
            
            # Create temporary directory for the task
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)
                
                # Create task description file
                task_file = temp_path / "task.md"
                task_content = self._create_task_description(task)
                task_file.write_text(task_content)
                
                # Run Codex CLI
                solution = await self._run_codex_cli(temp_path, task)
                
                # Update request count
                self.requests_made += 1
                
                # Log API call
                eval_logger.api_call(
                    self.evaluator_type.value,
                    self.estimate_cost(task)
                )
                
                return solution
                
        except Exception as e:
            raise Exception(f"Codex CLI execution failed: {str(e)}")
    
    async def _check_codex_availability(self):
        """Check if Codex CLI is available."""
        try:
            result = await asyncio.create_subprocess_exec(
                self.cli_command, "--version",
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await result.communicate()
            
            if result.returncode != 0:
                raise Exception("Codex CLI not found or not working")
                
        except FileNotFoundError:
            raise Exception("Codex CLI not installed. Please install it first.")
    
    def _create_task_description(self, task: SWEBenchTask) -> str:
        """Create a task description file for Codex."""
        return f"""# GitHub Issue Solution Task

## Repository
{task.repo}

## Issue ID
{task.instance_id}

## Problem Statement
{task.problem_statement}

## Additional Context
{task.hints_text}

## Instructions
Please analyze this GitHub issue and provide a complete solution. Focus on:

1. Understanding the problem requirements
2. Implementing the necessary code changes
3. Following best practices and existing code patterns
4. Providing clear, maintainable code

Please generate the solution code and explain your approach.
"""
    
    async def _run_codex_cli(self, work_dir: Path, task: SWEBenchTask) -> CodeSolution:
        """Run Codex CLI to generate solution."""
        
        # Prepare Codex CLI command
        cmd = [
            self.cli_command,
            "solve",
            "--task", str(work_dir / "task.md"),
            "--format", "json",
            "--timeout", str(config.task_timeout_seconds)
        ]
        
        try:
            # Run Codex CLI
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=work_dir
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode != 0:
                error_msg = stderr.decode() if stderr else "Unknown error"
                raise Exception(f"Codex CLI failed: {error_msg}")
            
            # Parse JSON output
            try:
                output = json.loads(stdout.decode())
                return self._parse_codex_output(output)
            except json.JSONDecodeError:
                # Fallback: treat stdout as raw code
                return CodeSolution(
                    code=stdout.decode(),
                    explanation="Generated by Codex CLI (raw output)",
                    files_modified=[],
                    confidence_score=0.7
                )
                
        except asyncio.TimeoutError:
            raise Exception(f"Codex CLI timed out after {config.task_timeout_seconds} seconds")
        except Exception as e:
            raise Exception(f"Error running Codex CLI: {str(e)}")
    
    def _parse_codex_output(self, output: Dict[str, Any]) -> CodeSolution:
        """Parse Codex CLI JSON output."""
        
        # Extract fields from Codex output
        code = output.get("code", "")
        explanation = output.get("explanation", "")
        files_modified = output.get("files_modified", [])
        confidence = output.get("confidence", 0.7)
        
        # Handle different possible output formats
        if not code and "solution" in output:
            code = output["solution"]
        
        if not explanation and "description" in output:
            explanation = output["description"]
        
        if not files_modified and "modified_files" in output:
            files_modified = output["modified_files"]
        
        # Ensure we have at least some code
        if not code:
            # Look for code in other fields
            for key in ["implementation", "patch", "changes"]:
                if key in output and output[key]:
                    code = output[key]
                    break
        
        if not code:
            raise Exception("No code solution found in Codex output")
        
        return CodeSolution(
            code=code,
            explanation=explanation or "Solution generated by Codex CLI",
            files_modified=files_modified,
            confidence_score=confidence
        )
    
    def _calculate_actual_cost(self, task: SWEBenchTask, solution: CodeSolution) -> float:
        """Calculate actual cost for Codex CLI usage.
        
        Since Codex CLI doesn't provide detailed token usage,
        we'll use the estimated cost.
        """
        return self.estimate_cost(task)

================
File: src/sandbox/__init__.py
================
"""Sandbox execution modules."""

from .e2b_manager import E2BSandboxManager
from .docker_manager import DockerSandboxManager

__all__ = [
    "E2BSandboxManager",
    "DockerSandboxManager"
]

================
File: src/sandbox/docker_manager.py
================
"""Docker sandbox management as fallback for E2B."""

import asyncio
import docker
import tempfile
import os
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any

from ..models import SWEBenchTask, CodeSolution, TestResult
from ..config import config
from ..utils import logger


class DockerSandboxManager:
    """Manager for Docker-based sandboxes."""
    
    def __init__(self):
        try:
            self.docker_client = docker.from_env()
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {str(e)}")
            raise Exception("Docker not available. Please install Docker or use E2B.")
        
        self.active_containers: Dict[str, docker.models.containers.Container] = {}
        self.temp_dirs: Dict[str, str] = {}
    
    async def create_sandbox(self, task_id: str, template: str = "python") -> str:
        """Create a new Docker container for a task.
        
        Args:
            task_id: Unique identifier for the task
            template: Container template to use
            
        Returns:
            Container ID
        """
        try:
            # Create temporary directory for this task
            temp_dir = tempfile.mkdtemp(prefix=f"swe_bench_{task_id}_")
            self.temp_dirs[task_id] = temp_dir
            
            # Choose appropriate Docker image
            image = self._get_docker_image(template)
            
            # Create and start container
            container = self.docker_client.containers.run(
                image,
                command="sleep infinity",  # Keep container running
                detach=True,
                volumes={temp_dir: {'bind': '/workspace', 'mode': 'rw'}},
                working_dir='/workspace',
                name=f"swe_bench_{task_id}",
                remove=True  # Auto-remove when stopped
            )
            
            self.active_containers[task_id] = container
            
            logger.info(f"Created Docker sandbox", 
                       task_id=task_id, 
                       container_id=container.id[:12],
                       image=image)
            
            return container.id
            
        except Exception as e:
            logger.error(f"Failed to create Docker sandbox", 
                        task_id=task_id, error=str(e))
            raise Exception(f"Failed to create Docker sandbox: {str(e)}")
    
    def _get_docker_image(self, template: str) -> str:
        """Get appropriate Docker image for template."""
        images = {
            "python": "python:3.9-slim",
            "node": "node:16-slim",
            "ubuntu": "ubuntu:20.04"
        }
        return images.get(template, "python:3.9-slim")
    
    async def setup_repository(self, task_id: str, task: SWEBenchTask) -> bool:
        """Set up the repository in the Docker container."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        
        try:
            # Install git if not present
            self._exec_command(container, "apt-get update && apt-get install -y git")
            
            # Clone the repository
            clone_cmd = f"git clone https://github.com/{task.repo}.git /workspace/repo"
            result = self._exec_command(container, clone_cmd)
            
            if result.exit_code != 0:
                raise Exception(f"Failed to clone repository: {result.output}")
            
            # Checkout the base commit
            if task.base_commit:
                checkout_cmd = f"cd /workspace/repo && git checkout {task.base_commit}"
                result = self._exec_command(container, checkout_cmd)
                
                if result.exit_code != 0:
                    logger.warning(f"Failed to checkout base commit", 
                                 task_id=task_id, 
                                 commit=task.base_commit,
                                 error=result.output)
            
            # Install Python dependencies
            self._install_python_dependencies(container)
            
            logger.info(f"Repository setup completed", task_id=task_id)
            return True
            
        except Exception as e:
            logger.error(f"Repository setup failed", 
                        task_id=task_id, error=str(e))
            return False
    
    def _install_python_dependencies(self, container):
        """Install Python dependencies in the container."""
        
        # Install pip if not present
        self._exec_command(container, "apt-get install -y python3-pip")
        
        # Common dependency installation commands
        dep_commands = [
            "cd /workspace/repo && [ -f requirements.txt ] && pip install -r requirements.txt || true",
            "cd /workspace/repo && [ -f setup.py ] && pip install -e . || true",
            "cd /workspace/repo && [ -f pyproject.toml ] && pip install -e . || true",
            "pip install pytest unittest2 nose"  # Common test frameworks
        ]
        
        for cmd in dep_commands:
            try:
                self._exec_command(container, cmd)
            except:
                continue  # Ignore failures
    
    async def apply_solution(self, task_id: str, solution: CodeSolution) -> bool:
        """Apply the generated solution to the repository."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        
        try:
            if solution.files_modified:
                for file_path in solution.files_modified:
                    self._update_file(container, file_path, solution.code)
            else:
                # Try to apply code intelligently
                self._apply_code_intelligently(container, solution.code)
            
            logger.info(f"Solution applied", 
                       task_id=task_id, 
                       files_modified=solution.files_modified)
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply solution", 
                        task_id=task_id, error=str(e))
            return False
    
    def _update_file(self, container, file_path: str, content: str):
        """Update a specific file with new content."""
        
        # Write content to a temporary file in the host temp directory
        task_id = self._get_task_id_for_container(container)
        temp_dir = self.temp_dirs[task_id]
        temp_file = os.path.join(temp_dir, "temp_update.py")
        
        with open(temp_file, 'w') as f:
            f.write(content)
        
        # Copy from temp location to target location in container
        copy_cmd = f"cp /workspace/temp_update.py /workspace/repo/{file_path}"
        self._exec_command(container, copy_cmd)
    
    def _get_task_id_for_container(self, container) -> str:
        """Get task ID for a container."""
        for task_id, cont in self.active_containers.items():
            if cont.id == container.id:
                return task_id
        raise Exception("Container not found in active containers")
    
    def _apply_code_intelligently(self, container, code: str):
        """Try to intelligently apply code to the repository."""
        
        # Look for Python files in the repository
        if "def " in code or "class " in code:
            find_cmd = "find /workspace/repo -name '*.py' -type f | head -5"
            result = self._exec_command(container, find_cmd)
            
            if result.exit_code == 0 and result.output:
                files = result.output.strip().split('\n')
                if files:
                    # Apply to the first Python file found
                    file_path = files[0].replace('/workspace/repo/', '')
                    self._update_file(container, file_path, code)
    
    async def run_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests in the Docker container."""
        if task_id not in self.active_containers:
            raise Exception(f"No active container for task {task_id}")
        
        container = self.active_containers[task_id]
        test_results = []
        
        try:
            # Run FAIL_TO_PASS tests
            for test_name in task.FAIL_TO_PASS:
                result = self._run_single_test(container, test_name)
                test_results.append(result)
            
            # Run PASS_TO_PASS tests
            for test_name in task.PASS_TO_PASS:
                result = self._run_single_test(container, test_name)
                test_results.append(result)
            
            logger.info(f"Tests completed", 
                       task_id=task_id, 
                       total_tests=len(test_results))
            
            return test_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
    
    def _run_single_test(self, container, test_name: str) -> TestResult:
        """Run a single test and return the result."""
        
        # Common test runners
        test_commands = [
            f"cd /workspace/repo && python -m pytest {test_name} -v",
            f"cd /workspace/repo && python -m unittest {test_name}",
            f"cd /workspace/repo && python {test_name}",
            f"cd /workspace/repo && pytest {test_name}"
        ]
        
        for cmd in test_commands:
            try:
                result = self._exec_command(container, cmd)
                
                # Determine test status
                status = "PASSED" if result.exit_code == 0 else "FAILED"
                
                return TestResult(
                    test_name=test_name,
                    status=status,
                    output=result.output,
                    execution_time=0.0,  # Docker doesn't provide timing
                    error_message=result.output if result.exit_code != 0 else None
                )
                
            except Exception:
                continue
        
        # If all test commands failed
        return TestResult(
            test_name=test_name,
            status="ERROR",
            output="",
            execution_time=0.0,
            error_message="Could not run test with any known test runner"
        )
    
    def _exec_command(self, container, command: str):
        """Execute a command in the container."""
        try:
            result = container.exec_run(command, workdir="/workspace")
            
            # Create a simple result object
            class ExecResult:
                def __init__(self, exit_code, output):
                    self.exit_code = exit_code
                    self.output = output.decode() if isinstance(output, bytes) else output
                    self.stdout = self.output
                    self.stderr = ""
            
            return ExecResult(result.exit_code, result.output)
            
        except Exception as e:
            logger.error(f"Command execution failed", 
                        command=command, error=str(e))
            raise
    
    async def cleanup_sandbox(self, task_id: str):
        """Clean up and remove the Docker container."""
        if task_id in self.active_containers:
            try:
                container = self.active_containers[task_id]
                container.stop()
                container.remove()
                del self.active_containers[task_id]
                
                logger.info(f"Cleaned up Docker container", task_id=task_id)
                
            except Exception as e:
                logger.error(f"Failed to cleanup container", 
                           task_id=task_id, error=str(e))
        
        # Clean up temporary directory
        if task_id in self.temp_dirs:
            try:
                shutil.rmtree(self.temp_dirs[task_id])
                del self.temp_dirs[task_id]
            except Exception as e:
                logger.error(f"Failed to cleanup temp directory", 
                           task_id=task_id, error=str(e))
    
    async def cleanup_all(self):
        """Clean up all active containers."""
        for task_id in list(self.active_containers.keys()):
            await self.cleanup_sandbox(task_id)

================
File: src/sandbox/e2b_manager.py
================
"""E2B sandbox management for secure code execution."""

import asyncio
from typing import Dict, List, Optional, Any
from e2b import Sandbox
import tempfile
import os
from pathlib import Path

from ..models import SWEBenchTask, CodeSolution, TestResult
from ..config import config
from ..utils import logger


class E2BSandboxManager:
    """Manager for E2B sandboxes."""
    
    def __init__(self):
        self.api_key = config.e2b_api_key
        self.active_sandboxes: Dict[str, Sandbox] = {}
        
    async def create_sandbox(self, task_id: str, template: str = "python") -> str:
        """Create a new E2B sandbox for a task.
        
        Args:
            task_id: Unique identifier for the task
            template: Sandbox template to use
            
        Returns:
            Sandbox ID
        """
        try:
            sandbox = Sandbox(
                template=template,
                api_key=self.api_key,
                timeout=config.sandbox_timeout_seconds
            )
            
            self.active_sandboxes[task_id] = sandbox
            
            logger.info(f"Created E2B sandbox", 
                       task_id=task_id, 
                       sandbox_id=sandbox.id,
                       template=template)
            
            return sandbox.id
            
        except Exception as e:
            logger.error(f"Failed to create E2B sandbox", 
                        task_id=task_id, error=str(e))
            raise Exception(f"Failed to create sandbox: {str(e)}")
    
    async def setup_repository(self, task_id: str, task: SWEBenchTask) -> bool:
        """Set up the repository in the sandbox.
        
        Args:
            task_id: Task identifier
            task: SWE-bench task with repository information
            
        Returns:
            True if setup successful
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        
        try:
            # Clone the repository
            clone_cmd = f"git clone https://github.com/{task.repo}.git /repo"
            result = await self._run_command(sandbox, clone_cmd)
            
            if result.exit_code != 0:
                raise Exception(f"Failed to clone repository: {result.stderr}")
            
            # Checkout the base commit
            if task.base_commit:
                checkout_cmd = f"cd /repo && git checkout {task.base_commit}"
                result = await self._run_command(sandbox, checkout_cmd)
                
                if result.exit_code != 0:
                    logger.warning(f"Failed to checkout base commit", 
                                 task_id=task_id, 
                                 commit=task.base_commit,
                                 error=result.stderr)
            
            # Set up environment if specified
            if task.environment_setup_commit:
                setup_cmd = f"cd /repo && git checkout {task.environment_setup_commit}"
                await self._run_command(sandbox, setup_cmd)
            
            # Install dependencies (common patterns)
            await self._install_dependencies(sandbox)
            
            logger.info(f"Repository setup completed", task_id=task_id)
            return True
            
        except Exception as e:
            logger.error(f"Repository setup failed", 
                        task_id=task_id, error=str(e))
            return False
    
    async def apply_solution(self, task_id: str, solution: CodeSolution) -> bool:
        """Apply the generated solution to the repository.
        
        Args:
            task_id: Task identifier
            solution: Generated code solution
            
        Returns:
            True if solution applied successfully
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        
        try:
            # If specific files are mentioned, update them
            if solution.files_modified:
                for file_path in solution.files_modified:
                    await self._update_file(sandbox, file_path, solution.code)
            else:
                # Try to determine files to modify from the code
                await self._apply_code_intelligently(sandbox, solution.code)
            
            logger.info(f"Solution applied", 
                       task_id=task_id, 
                       files_modified=solution.files_modified)
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply solution", 
                        task_id=task_id, error=str(e))
            return False
    
    async def run_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests in the sandbox.
        
        Args:
            task_id: Task identifier
            task: SWE-bench task with test information
            
        Returns:
            List of test results
        """
        if task_id not in self.active_sandboxes:
            raise Exception(f"No active sandbox for task {task_id}")
        
        sandbox = self.active_sandboxes[task_id]
        test_results = []
        
        try:
            # Run FAIL_TO_PASS tests (these should pass after our solution)
            for test_name in task.FAIL_TO_PASS:
                result = await self._run_single_test(sandbox, test_name)
                test_results.append(result)
            
            # Run PASS_TO_PASS tests (these should continue to pass)
            for test_name in task.PASS_TO_PASS:
                result = await self._run_single_test(sandbox, test_name)
                test_results.append(result)
            
            logger.info(f"Tests completed", 
                       task_id=task_id, 
                       total_tests=len(test_results))
            
            return test_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
    
    async def _run_single_test(self, sandbox: Sandbox, test_name: str) -> TestResult:
        """Run a single test and return the result."""
        
        # Common test runners
        test_commands = [
            f"cd /repo && python -m pytest {test_name} -v",
            f"cd /repo && python -m unittest {test_name}",
            f"cd /repo && python {test_name}",
            f"cd /repo && pytest {test_name}"
        ]
        
        for cmd in test_commands:
            try:
                result = await self._run_command(sandbox, cmd)
                
                # Determine test status
                status = "PASSED" if result.exit_code == 0 else "FAILED"
                
                return TestResult(
                    test_name=test_name,
                    status=status,
                    output=result.stdout + result.stderr,
                    execution_time=0.0,  # E2B doesn't provide timing
                    error_message=result.stderr if result.exit_code != 0 else None
                )
                
            except Exception as e:
                continue
        
        # If all test commands failed
        return TestResult(
            test_name=test_name,
            status="ERROR",
            output="",
            execution_time=0.0,
            error_message="Could not run test with any known test runner"
        )
    
    async def _run_command(self, sandbox: Sandbox, command: str, timeout: int = 60):
        """Run a command in the sandbox."""
        try:
            result = sandbox.process.start_and_wait(
                cmd=command,
                timeout=timeout
            )
            return result
        except Exception as e:
            logger.error(f"Command execution failed", 
                        command=command, error=str(e))
            raise
    
    async def _install_dependencies(self, sandbox: Sandbox):
        """Install common dependencies in the repository."""
        
        dependency_commands = [
            "cd /repo && [ -f requirements.txt ] && pip install -r requirements.txt",
            "cd /repo && [ -f setup.py ] && pip install -e .",
            "cd /repo && [ -f pyproject.toml ] && pip install -e .",
            "cd /repo && [ -f package.json ] && npm install",
            "cd /repo && [ -f Pipfile ] && pipenv install"
        ]
        
        for cmd in dependency_commands:
            try:
                await self._run_command(sandbox, cmd)
            except:
                continue  # Ignore failures, not all repos will have these files
    
    async def _update_file(self, sandbox: Sandbox, file_path: str, content: str):
        """Update a specific file with new content."""
        
        # Create a temporary file with the content
        temp_file = f"/tmp/update_{hash(file_path)}.py"
        
        # Write content to temp file
        write_cmd = f"cat > {temp_file} << 'EOF'\n{content}\nEOF"
        await self._run_command(sandbox, write_cmd)
        
        # Copy to target location
        copy_cmd = f"cp {temp_file} /repo/{file_path}"
        await self._run_command(sandbox, copy_cmd)
    
    async def _apply_code_intelligently(self, sandbox: Sandbox, code: str):
        """Try to intelligently apply code to the repository."""
        
        # This is a simplified approach - in practice, you'd want more
        # sophisticated code parsing and file detection
        
        # Look for Python files in the code
        if "def " in code or "class " in code:
            # Assume it's Python code, try to find appropriate file
            find_cmd = "find /repo -name '*.py' -type f | head -5"
            result = await self._run_command(sandbox, find_cmd)
            
            if result.exit_code == 0 and result.stdout:
                files = result.stdout.strip().split('\n')
                if files:
                    # Apply to the first Python file found
                    await self._update_file(sandbox, files[0].replace('/repo/', ''), code)
    
    async def cleanup_sandbox(self, task_id: str):
        """Clean up and close the sandbox."""
        if task_id in self.active_sandboxes:
            try:
                sandbox = self.active_sandboxes[task_id]
                sandbox.close()
                del self.active_sandboxes[task_id]
                
                logger.info(f"Cleaned up sandbox", task_id=task_id)
                
            except Exception as e:
                logger.error(f"Failed to cleanup sandbox", 
                           task_id=task_id, error=str(e))
    
    async def cleanup_all(self):
        """Clean up all active sandboxes."""
        for task_id in list(self.active_sandboxes.keys()):
            await self.cleanup_sandbox(task_id)

================
File: src/swe_bench/__init__.py
================
"""SWE-bench integration modules."""

from .task_loader import SWEBenchLoader
from .test_runner import TestRunner
from .result_validator import ResultValidator

__all__ = [
    "SWEBenchLoader",
    "TestRunner", 
    "ResultValidator"
]

================
File: src/swe_bench/result_validator.py
================
"""Result validation and analysis for SWE-bench evaluations."""

from typing import List, Dict, Any, Optional
from datetime import datetime
import json

from ..models import (
    EvaluationResult, 
    EvaluationSummary, 
    ComparisonReport,
    EvaluatorType,
    TaskStatus
)
from ..config import config
from ..utils import logger


class ResultValidator:
    """Validates and analyzes evaluation results."""
    
    def __init__(self):
        self.results_path = config.results_output_path
    
    def validate_evaluation_result(self, result: EvaluationResult) -> Dict[str, Any]:
        """Validate a single evaluation result.
        
        Args:
            result: Evaluation result to validate
            
        Returns:
            Validation report
        """
        validation_report = {
            "task_id": result.task_id,
            "evaluator_type": result.evaluator_type.value,
            "is_valid": True,
            "issues": [],
            "metrics": {}
        }
        
        # Check basic validity
        if result.status == TaskStatus.COMPLETED:
            if not result.solution:
                validation_report["is_valid"] = False
                validation_report["issues"].append("Completed task missing solution")
            
            if not result.test_results:
                validation_report["issues"].append("No test results available")
            
            # Calculate SWE-bench specific metrics
            if result.test_results:
                metrics = self._calculate_swe_bench_metrics(result)
                validation_report["metrics"] = metrics
        
        elif result.status == TaskStatus.FAILED:
            if not result.error_message:
                validation_report["issues"].append("Failed task missing error message")
        
        # Check cost reasonableness
        if result.api_cost > 10.0:  # $10 seems high for a single task
            validation_report["issues"].append(f"High API cost: ${result.api_cost:.2f}")
        
        # Check execution time
        if result.execution_time > 600:  # 10 minutes seems long
            validation_report["issues"].append(f"Long execution time: {result.execution_time:.1f}s")
        
        return validation_report
    
    def _calculate_swe_bench_metrics(self, result: EvaluationResult) -> Dict[str, Any]:
        """Calculate SWE-bench specific metrics from test results."""
        
        if not result.test_results:
            return {}
        
        total_tests = len(result.test_results)
        passed_tests = sum(1 for r in result.test_results if r.status == "PASSED")
        failed_tests = sum(1 for r in result.test_results if r.status == "FAILED")
        error_tests = sum(1 for r in result.test_results if r.status == "ERROR")
        
        # Extract SWE-bench correctness from test outputs
        correct_tests = 0
        for test_result in result.test_results:
            if "[EVALUATION] Test correctness: CORRECT" in test_result.output:
                correct_tests += 1
        
        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "correct_tests": correct_tests,
            "pass_rate": passed_tests / max(1, total_tests),
            "correctness_rate": correct_tests / max(1, total_tests),
            "swe_bench_success": correct_tests == total_tests and total_tests > 0
        }
    
    def create_evaluation_summary(self, 
                                results: List[EvaluationResult],
                                evaluator_type: EvaluatorType) -> EvaluationSummary:
        """Create summary from evaluation results.
        
        Args:
            results: List of evaluation results
            evaluator_type: Type of evaluator
            
        Returns:
            Evaluation summary
        """
        
        total_tasks = len(results)
        completed_tasks = sum(1 for r in results if r.status == TaskStatus.COMPLETED)
        failed_tasks = sum(1 for r in results if r.status == TaskStatus.FAILED)
        
        # Aggregate metrics
        total_tests_passed = 0
        total_tests_failed = 0
        total_execution_time = 0.0
        total_api_cost = 0.0
        
        for result in results:
            if result.test_results:
                total_tests_passed += sum(1 for r in result.test_results if r.status == "PASSED")
                total_tests_failed += sum(1 for r in result.test_results if r.status == "FAILED")
            
            total_execution_time += result.execution_time
            total_api_cost += result.api_cost
        
        summary = EvaluationSummary(
            evaluator_type=evaluator_type,
            total_tasks=total_tasks,
            completed_tasks=completed_tasks,
            failed_tasks=failed_tasks,
            total_tests_passed=total_tests_passed,
            total_tests_failed=total_tests_failed,
            total_execution_time=total_execution_time,
            total_api_cost=total_api_cost,
            results=results
        )
        
        return summary
    
    def create_comparison_report(self, 
                               summaries: Dict[EvaluatorType, EvaluationSummary]) -> ComparisonReport:
        """Create comparison report from multiple evaluation summaries.
        
        Args:
            summaries: Dictionary of evaluator summaries
            
        Returns:
            Comparison report
        """
        
        report = ComparisonReport(summaries=summaries)
        
        logger.info(f"Created comparison report", 
                   evaluators=list(summaries.keys()),
                   total_evaluators=len(summaries))
        
        return report
    
    def save_results(self, 
                    results: List[EvaluationResult],
                    filename: Optional[str] = None) -> str:
        """Save evaluation results to file.
        
        Args:
            results: List of evaluation results
            filename: Optional filename (auto-generated if not provided)
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"evaluation_results_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert results to serializable format
        serializable_results = []
        for result in results:
            result_dict = result.dict()
            # Convert datetime objects to strings
            if result_dict.get('started_at'):
                result_dict['started_at'] = result_dict['started_at'].isoformat()
            if result_dict.get('completed_at'):
                result_dict['completed_at'] = result_dict['completed_at'].isoformat()
            serializable_results.append(result_dict)
        
        with open(filepath, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        logger.info(f"Saved evaluation results", 
                   filepath=str(filepath), 
                   num_results=len(results))
        
        return str(filepath)
    
    def save_summary(self, 
                    summary: EvaluationSummary,
                    filename: Optional[str] = None) -> str:
        """Save evaluation summary to file.
        
        Args:
            summary: Evaluation summary
            filename: Optional filename
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            evaluator = summary.evaluator_type.value
            filename = f"evaluation_summary_{evaluator}_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert to serializable format
        summary_dict = summary.dict()
        
        with open(filepath, 'w') as f:
            json.dump(summary_dict, f, indent=2)
        
        logger.info(f"Saved evaluation summary", 
                   filepath=str(filepath),
                   evaluator_type=summary.evaluator_type.value)
        
        return str(filepath)
    
    def save_comparison_report(self, 
                             report: ComparisonReport,
                             filename: Optional[str] = None) -> str:
        """Save comparison report to file.
        
        Args:
            report: Comparison report
            filename: Optional filename
            
        Returns:
            Path to saved file
        """
        
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"comparison_report_{timestamp}.json"
        
        filepath = self.results_path / filename
        
        # Convert to serializable format
        report_dict = report.dict()
        report_dict['generated_at'] = report_dict['generated_at'].isoformat()
        
        with open(filepath, 'w') as f:
            json.dump(report_dict, f, indent=2)
        
        logger.info(f"Saved comparison report", 
                   filepath=str(filepath),
                   num_evaluators=len(report.summaries))
        
        return str(filepath)
    
    def load_results(self, filepath: str) -> List[EvaluationResult]:
        """Load evaluation results from file.
        
        Args:
            filepath: Path to results file
            
        Returns:
            List of evaluation results
        """
        
        with open(filepath, 'r') as f:
            data = json.load(f)
        
        results = []
        for item in data:
            # Convert datetime strings back to datetime objects
            if item.get('started_at'):
                item['started_at'] = datetime.fromisoformat(item['started_at'])
            if item.get('completed_at'):
                item['completed_at'] = datetime.fromisoformat(item['completed_at'])
            
            result = EvaluationResult(**item)
            results.append(result)
        
        logger.info(f"Loaded evaluation results", 
                   filepath=filepath, 
                   num_results=len(results))
        
        return results
    
    def generate_report_summary(self, report: ComparisonReport) -> str:
        """Generate a human-readable summary of the comparison report.
        
        Args:
            report: Comparison report
            
        Returns:
            Human-readable summary string
        """
        
        lines = []
        lines.append("=== SWE-bench Evaluation Comparison Report ===")
        lines.append(f"Generated: {report.generated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        lines.append("")
        
        # Overall comparison
        lines.append("Overall Performance:")
        for evaluator_type, summary in report.summaries.items():
            lines.append(f"  {evaluator_type.value}:")
            lines.append(f"    Completion Rate: {summary.completion_rate:.1%}")
            lines.append(f"    Success Rate: {summary.overall_success_rate:.1%}")
            lines.append(f"    Total Cost: ${summary.total_api_cost:.2f}")
            lines.append(f"    Avg Time/Task: {summary.average_execution_time:.1f}s")
            lines.append("")
        
        # Best performers
        best_success = report.get_best_performer("success_rate")
        best_completion = report.get_best_performer("completion_rate")
        best_cost = report.get_best_performer("cost_efficiency")
        
        lines.append("Best Performers:")
        if best_success:
            lines.append(f"  Highest Success Rate: {best_success.value}")
        if best_completion:
            lines.append(f"  Highest Completion Rate: {best_completion.value}")
        if best_cost:
            lines.append(f"  Most Cost Efficient: {best_cost.value}")
        
        return "\n".join(lines)

================
File: src/swe_bench/task_loader.py
================
"""SWE-bench task loading and management."""

import json
import requests
from pathlib import Path
from typing import List, Optional, Dict, Any
import pandas as pd

from ..models import SWEBenchTask
from ..config import config
from ..utils import logger


class SWEBenchLoader:
    """Loader for SWE-bench dataset."""
    
    def __init__(self):
        self.dataset_path = config.swe_bench_dataset_path
        self.cache_file = self.dataset_path / "swe_bench_cache.json"
        
    async def load_tasks(self, 
                        subset: str = "lite", 
                        max_tasks: Optional[int] = None,
                        filter_repos: Optional[List[str]] = None) -> List[SWEBenchTask]:
        """Load SWE-bench tasks.
        
        Args:
            subset: Dataset subset ("lite", "verified", "full")
            max_tasks: Maximum number of tasks to load
            filter_repos: List of repositories to filter by
            
        Returns:
            List of SWE-bench tasks
        """
        logger.info(f"Loading SWE-bench tasks", subset=subset, max_tasks=max_tasks)
        
        # Try to load from cache first
        tasks = await self._load_from_cache(subset)
        
        if not tasks:
            # Download and cache the dataset
            tasks = await self._download_dataset(subset)
            await self._save_to_cache(subset, tasks)
        
        # Apply filters
        if filter_repos:
            tasks = [task for task in tasks if task.repo in filter_repos]
            logger.info(f"Filtered tasks by repositories", 
                       repos=filter_repos, remaining_tasks=len(tasks))
        
        # Limit number of tasks
        if max_tasks and len(tasks) > max_tasks:
            tasks = tasks[:max_tasks]
            logger.info(f"Limited tasks to {max_tasks}")
        
        logger.info(f"Loaded {len(tasks)} SWE-bench tasks")
        return tasks
    
    async def _load_from_cache(self, subset: str) -> Optional[List[SWEBenchTask]]:
        """Load tasks from cache file."""
        if not self.cache_file.exists():
            return None
        
        try:
            with open(self.cache_file, 'r') as f:
                cache_data = json.load(f)
            
            if subset not in cache_data:
                return None
            
            tasks = []
            for task_data in cache_data[subset]:
                try:
                    task = SWEBenchTask(**task_data)
                    tasks.append(task)
                except Exception as e:
                    logger.warning(f"Failed to parse cached task", error=str(e))
                    continue
            
            logger.info(f"Loaded {len(tasks)} tasks from cache")
            return tasks
            
        except Exception as e:
            logger.warning(f"Failed to load from cache", error=str(e))
            return None
    
    async def _save_to_cache(self, subset: str, tasks: List[SWEBenchTask]):
        """Save tasks to cache file."""
        try:
            # Load existing cache
            cache_data = {}
            if self.cache_file.exists():
                with open(self.cache_file, 'r') as f:
                    cache_data = json.load(f)
            
            # Update cache with new data
            cache_data[subset] = [task.dict() for task in tasks]
            
            # Save cache
            with open(self.cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
            
            logger.info(f"Saved {len(tasks)} tasks to cache")
            
        except Exception as e:
            logger.warning(f"Failed to save to cache", error=str(e))
    
    async def _download_dataset(self, subset: str) -> List[SWEBenchTask]:
        """Download SWE-bench dataset from official source."""
        
        # SWE-bench dataset URLs
        urls = {
            "lite": "https://raw.githubusercontent.com/princeton-nlp/SWE-bench/main/swebench/collect/data/swe-bench-lite.json",
            "verified": "https://raw.githubusercontent.com/princeton-nlp/SWE-bench/main/swebench/collect/data/swe-bench-verified.json",
            "full": "https://raw.githubusercontent.com/princeton-nlp/SWE-bench/main/swebench/collect/data/swe-bench.json"
        }
        
        if subset not in urls:
            raise ValueError(f"Unknown subset: {subset}. Available: {list(urls.keys())}")
        
        url = urls[subset]
        logger.info(f"Downloading SWE-bench dataset", subset=subset, url=url)
        
        try:
            response = requests.get(url, timeout=60)
            response.raise_for_status()
            
            # Parse JSON data
            data = response.json()
            
            # Convert to SWEBenchTask objects
            tasks = []
            for item in data:
                try:
                    task = self._parse_task_data(item)
                    tasks.append(task)
                except Exception as e:
                    logger.warning(f"Failed to parse task", 
                                 instance_id=item.get('instance_id', 'unknown'),
                                 error=str(e))
                    continue
            
            logger.info(f"Downloaded {len(tasks)} tasks from {subset} dataset")
            return tasks
            
        except requests.RequestException as e:
            raise Exception(f"Failed to download dataset: {str(e)}")
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse dataset JSON: {str(e)}")
    
    def _parse_task_data(self, data: Dict[str, Any]) -> SWEBenchTask:
        """Parse raw task data into SWEBenchTask object."""
        
        # Handle different possible field names
        instance_id = data.get('instance_id') or data.get('id')
        repo = data.get('repo') or data.get('repository')
        base_commit = data.get('base_commit') or data.get('commit')
        problem_statement = data.get('problem_statement') or data.get('description', '')
        hints_text = data.get('hints_text', '')
        created_at = data.get('created_at', '')
        version = data.get('version', '1.0')
        
        # Test information
        fail_to_pass = data.get('FAIL_TO_PASS', [])
        pass_to_pass = data.get('PASS_TO_PASS', [])
        
        # Additional fields
        environment_setup_commit = data.get('environment_setup_commit', '')
        patch = data.get('patch', '')
        test_patch = data.get('test_patch', '')
        
        if not instance_id:
            raise ValueError("Missing instance_id")
        if not repo:
            raise ValueError("Missing repo")
        if not problem_statement:
            raise ValueError("Missing problem_statement")
        
        return SWEBenchTask(
            instance_id=instance_id,
            repo=repo,
            base_commit=base_commit or '',
            problem_statement=problem_statement,
            hints_text=hints_text,
            created_at=created_at,
            version=version,
            FAIL_TO_PASS=fail_to_pass,
            PASS_TO_PASS=pass_to_pass,
            environment_setup_commit=environment_setup_commit,
            patch=patch,
            test_patch=test_patch
        )
    
    def get_task_by_id(self, tasks: List[SWEBenchTask], instance_id: str) -> Optional[SWEBenchTask]:
        """Get a specific task by instance ID."""
        for task in tasks:
            if task.instance_id == instance_id:
                return task
        return None
    
    def get_tasks_by_repo(self, tasks: List[SWEBenchTask], repo: str) -> List[SWEBenchTask]:
        """Get all tasks for a specific repository."""
        return [task for task in tasks if task.repo == repo]
    
    def get_task_statistics(self, tasks: List[SWEBenchTask]) -> Dict[str, Any]:
        """Get statistics about the loaded tasks."""
        if not tasks:
            return {}
        
        repos = [task.repo for task in tasks]
        repo_counts = {}
        for repo in repos:
            repo_counts[repo] = repo_counts.get(repo, 0) + 1
        
        return {
            "total_tasks": len(tasks),
            "unique_repos": len(set(repos)),
            "repo_distribution": repo_counts,
            "avg_problem_length": sum(len(task.problem_statement) for task in tasks) / len(tasks),
            "tasks_with_hints": sum(1 for task in tasks if task.hints_text),
            "tasks_with_tests": sum(1 for task in tasks if task.FAIL_TO_PASS or task.PASS_TO_PASS)
        }

================
File: src/swe_bench/test_runner.py
================
"""Test execution and validation for SWE-bench tasks."""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime

from ..models import SWEBenchTask, CodeSolution, TestResult, EvaluationResult
from ..sandbox import E2BSandboxManager, DockerSandboxManager
from ..config import config
from ..utils import logger, eval_logger


class TestRunner:
    """Runs tests for SWE-bench tasks in sandboxed environments."""
    
    def __init__(self, use_e2b: bool = True):
        self.use_e2b = use_e2b
        if use_e2b:
            self.sandbox_manager = E2BSandboxManager()
        else:
            self.sandbox_manager = DockerSandboxManager()
    
    async def run_task_tests(self, 
                           task: SWEBenchTask, 
                           solution: CodeSolution,
                           task_id: Optional[str] = None) -> List[TestResult]:
        """Run all tests for a task with the given solution.
        
        Args:
            task: SWE-bench task
            solution: Generated code solution
            task_id: Optional task identifier for sandbox management
            
        Returns:
            List of test results
        """
        if not task_id:
            task_id = f"{task.instance_id}_{datetime.now().timestamp()}"
        
        try:
            # Create sandbox
            await self.sandbox_manager.create_sandbox(task_id)
            
            # Set up repository
            setup_success = await self.sandbox_manager.setup_repository(task_id, task)
            if not setup_success:
                raise Exception("Failed to set up repository in sandbox")
            
            # Run tests before applying solution (baseline)
            baseline_results = await self._run_baseline_tests(task_id, task)
            
            # Apply the solution
            apply_success = await self.sandbox_manager.apply_solution(task_id, solution)
            if not apply_success:
                raise Exception("Failed to apply solution to repository")
            
            # Run tests after applying solution
            solution_results = await self.sandbox_manager.run_tests(task_id, task)
            
            # Combine and analyze results
            final_results = self._analyze_test_results(
                task, baseline_results, solution_results
            )
            
            return final_results
            
        except Exception as e:
            logger.error(f"Test execution failed", 
                        task_id=task_id, error=str(e))
            return []
        finally:
            # Always cleanup sandbox
            await self.sandbox_manager.cleanup_sandbox(task_id)
    
    async def _run_baseline_tests(self, task_id: str, task: SWEBenchTask) -> List[TestResult]:
        """Run tests before applying solution to establish baseline."""
        
        try:
            baseline_results = await self.sandbox_manager.run_tests(task_id, task)
            
            logger.info(f"Baseline tests completed", 
                       task_id=task_id,
                       total_tests=len(baseline_results),
                       failed_tests=sum(1 for r in baseline_results if r.status == "FAILED"))
            
            return baseline_results
            
        except Exception as e:
            logger.warning(f"Baseline test execution failed", 
                          task_id=task_id, error=str(e))
            return []
    
    def _analyze_test_results(self, 
                            task: SWEBenchTask,
                            baseline_results: List[TestResult],
                            solution_results: List[TestResult]) -> List[TestResult]:
        """Analyze test results to determine if solution is correct.
        
        SWE-bench evaluation criteria:
        - FAIL_TO_PASS tests should fail in baseline and pass after solution
        - PASS_TO_PASS tests should pass in both baseline and after solution
        """
        
        # Create lookup for baseline results
        baseline_lookup = {r.test_name: r for r in baseline_results}
        
        analyzed_results = []
        
        for result in solution_results:
            test_name = result.test_name
            baseline_result = baseline_lookup.get(test_name)
            
            # Determine if this test behaved correctly
            is_correct = self._is_test_result_correct(
                task, test_name, baseline_result, result
            )
            
            # Add metadata to result
            analyzed_result = TestResult(
                test_name=result.test_name,
                status=result.status,
                output=result.output,
                execution_time=result.execution_time,
                error_message=result.error_message
            )
            
            # Add correctness information to output
            correctness_info = f"\n[EVALUATION] Test correctness: {'CORRECT' if is_correct else 'INCORRECT'}"
            if baseline_result:
                correctness_info += f" (Baseline: {baseline_result.status} -> Solution: {result.status})"
            analyzed_result.output += correctness_info
            
            analyzed_results.append(analyzed_result)
        
        return analyzed_results
    
    def _is_test_result_correct(self, 
                              task: SWEBenchTask,
                              test_name: str,
                              baseline_result: Optional[TestResult],
                              solution_result: TestResult) -> bool:
        """Determine if a test result is correct according to SWE-bench criteria."""
        
        if test_name in task.FAIL_TO_PASS:
            # This test should fail in baseline and pass after solution
            if not baseline_result:
                # No baseline - assume it was failing
                return solution_result.status == "PASSED"
            
            return (baseline_result.status in ["FAILED", "ERROR"] and 
                   solution_result.status == "PASSED")
        
        elif test_name in task.PASS_TO_PASS:
            # This test should pass in both baseline and after solution
            if not baseline_result:
                # No baseline - just check if it passes now
                return solution_result.status == "PASSED"
            
            return (baseline_result.status == "PASSED" and 
                   solution_result.status == "PASSED")
        
        else:
            # Test not in expected lists - consider it correct if it passes
            return solution_result.status == "PASSED"
    
    async def validate_solution(self, 
                              task: SWEBenchTask, 
                              solution: CodeSolution) -> Dict[str, Any]:
        """Validate a solution against SWE-bench criteria.
        
        Returns:
            Dictionary with validation results
        """
        
        test_results = await self.run_task_tests(task, solution)
        
        # Calculate metrics
        total_tests = len(test_results)
        passed_tests = sum(1 for r in test_results if r.status == "PASSED")
        failed_tests = sum(1 for r in test_results if r.status == "FAILED")
        error_tests = sum(1 for r in test_results if r.status == "ERROR")
        
        # Check FAIL_TO_PASS tests specifically
        fail_to_pass_results = [r for r in test_results if r.test_name in task.FAIL_TO_PASS]
        fail_to_pass_passed = sum(1 for r in fail_to_pass_results if r.status == "PASSED")
        
        # Check PASS_TO_PASS tests specifically
        pass_to_pass_results = [r for r in test_results if r.test_name in task.PASS_TO_PASS]
        pass_to_pass_passed = sum(1 for r in pass_to_pass_results if r.status == "PASSED")
        
        # Determine overall success
        fail_to_pass_success = (len(task.FAIL_TO_PASS) == 0 or 
                               fail_to_pass_passed == len(task.FAIL_TO_PASS))
        pass_to_pass_success = (len(task.PASS_TO_PASS) == 0 or 
                               pass_to_pass_passed == len(task.PASS_TO_PASS))
        
        overall_success = fail_to_pass_success and pass_to_pass_success
        
        validation_result = {
            "overall_success": overall_success,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": passed_tests / max(1, total_tests),
            
            # SWE-bench specific metrics
            "fail_to_pass_total": len(task.FAIL_TO_PASS),
            "fail_to_pass_passed": fail_to_pass_passed,
            "fail_to_pass_success": fail_to_pass_success,
            
            "pass_to_pass_total": len(task.PASS_TO_PASS),
            "pass_to_pass_passed": pass_to_pass_passed,
            "pass_to_pass_success": pass_to_pass_success,
            
            "test_results": test_results
        }
        
        # Log validation results
        eval_logger.logger.info(
            "Solution validation completed",
            task_id=task.instance_id,
            overall_success=overall_success,
            success_rate=validation_result["success_rate"],
            **{k: v for k, v in validation_result.items() 
               if k not in ["test_results", "overall_success", "success_rate"]}
        )
        
        return validation_result
    
    async def batch_validate_solutions(self, 
                                     tasks_and_solutions: List[tuple[SWEBenchTask, CodeSolution]],
                                     max_concurrent: int = 3) -> List[Dict[str, Any]]:
        """Validate multiple solutions concurrently.
        
        Args:
            tasks_and_solutions: List of (task, solution) tuples
            max_concurrent: Maximum concurrent validations
            
        Returns:
            List of validation results
        """
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def validate_with_semaphore(task_solution_pair):
            task, solution = task_solution_pair
            async with semaphore:
                return await self.validate_solution(task, solution)
        
        # Create validation tasks
        validation_tasks = [
            validate_with_semaphore(pair) for pair in tasks_and_solutions
        ]
        
        # Execute all validations concurrently
        results = await asyncio.gather(*validation_tasks, return_exceptions=True)
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                task, _ = tasks_and_solutions[i]
                error_result = {
                    "overall_success": False,
                    "error": str(result),
                    "task_id": task.instance_id
                }
                final_results.append(error_result)
            else:
                final_results.append(result)
        
        return final_results

================
File: src/utils/__init__.py
================
"""Utility modules for the evaluation framework."""

from .logger import logger, eval_logger, EvaluationLogger
from .rate_limiter import rate_limiter, budget_tracker, RateLimiter, BudgetTracker

__all__ = [
    "logger",
    "eval_logger", 
    "EvaluationLogger",
    "rate_limiter",
    "budget_tracker",
    "RateLimiter",
    "BudgetTracker"
]

================
File: src/utils/logger.py
================
"""Logging configuration for the evaluation framework."""

import logging
import structlog
from pathlib import Path
from typing import Any, Dict

from ..config import config


def setup_logging() -> structlog.BoundLogger:
    """Set up structured logging."""
    
    # Configure standard logging
    logging.basicConfig(
        level=getattr(logging, config.log_level.upper()),
        format="%(asctime)s [%(levelname)s] %(name)s: %(message)s",
        handlers=[
            logging.FileHandler(config.log_file),
            logging.StreamHandler()
        ]
    )
    
    # Configure structlog
    structlog.configure(
        processors=[
            structlog.stdlib.filter_by_level,
            structlog.stdlib.add_logger_name,
            structlog.stdlib.add_log_level,
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.StackInfoRenderer(),
            structlog.processors.format_exc_info,
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer()
        ],
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger()


# Global logger instance
logger = setup_logging()


class EvaluationLogger:
    """Specialized logger for evaluation events."""
    
    def __init__(self):
        self.logger = structlog.get_logger("evaluation")
    
    def task_started(self, task_id: str, evaluator_type: str):
        """Log task start."""
        self.logger.info(
            "Task started",
            task_id=task_id,
            evaluator_type=evaluator_type,
            event="task_started"
        )
    
    def task_completed(self, task_id: str, evaluator_type: str, 
                      success_rate: float, execution_time: float, cost: float):
        """Log task completion."""
        self.logger.info(
            "Task completed",
            task_id=task_id,
            evaluator_type=evaluator_type,
            success_rate=success_rate,
            execution_time=execution_time,
            cost=cost,
            event="task_completed"
        )
    
    def task_failed(self, task_id: str, evaluator_type: str, error: str):
        """Log task failure."""
        self.logger.error(
            "Task failed",
            task_id=task_id,
            evaluator_type=evaluator_type,
            error=error,
            event="task_failed"
        )
    
    def api_call(self, evaluator_type: str, cost: float, tokens_used: int = 0):
        """Log API call metrics."""
        self.logger.debug(
            "API call made",
            evaluator_type=evaluator_type,
            cost=cost,
            tokens_used=tokens_used,
            event="api_call"
        )
    
    def test_result(self, task_id: str, test_name: str, status: str, 
                   execution_time: float):
        """Log individual test result."""
        self.logger.debug(
            "Test executed",
            task_id=task_id,
            test_name=test_name,
            status=status,
            execution_time=execution_time,
            event="test_result"
        )
    
    def budget_warning(self, current_cost: float, max_budget: float):
        """Log budget warning."""
        self.logger.warning(
            "Budget warning",
            current_cost=current_cost,
            max_budget=max_budget,
            percentage_used=(current_cost / max_budget) * 100,
            event="budget_warning"
        )
    
    def evaluation_summary(self, evaluator_type: str, summary_data: Dict[str, Any]):
        """Log evaluation summary."""
        self.logger.info(
            "Evaluation summary",
            evaluator_type=evaluator_type,
            **summary_data,
            event="evaluation_summary"
        )


# Global evaluation logger instance
eval_logger = EvaluationLogger()

================
File: src/utils/rate_limiter.py
================
"""Rate limiting utilities for API calls."""

import asyncio
import time
from typing import Dict, Optional
from asyncio_throttle import Throttler


class RateLimiter:
    """Rate limiter for API calls to prevent exceeding limits."""
    
    def __init__(self):
        self.throttlers: Dict[str, Throttler] = {}
        self.call_history: Dict[str, list] = {}
    
    def get_throttler(self, service: str, requests_per_minute: int) -> Throttler:
        """Get or create a throttler for a service."""
        if service not in self.throttlers:
            # Convert requests per minute to requests per second
            rate_limit = requests_per_minute / 60.0
            self.throttlers[service] = Throttler(rate_limit=rate_limit)
            self.call_history[service] = []
        
        return self.throttlers[service]
    
    async def acquire(self, service: str, requests_per_minute: int):
        """Acquire permission to make an API call."""
        throttler = self.get_throttler(service, requests_per_minute)
        
        # Record the call
        current_time = time.time()
        self.call_history[service].append(current_time)
        
        # Clean old entries (older than 1 minute)
        cutoff_time = current_time - 60
        self.call_history[service] = [
            t for t in self.call_history[service] if t > cutoff_time
        ]
        
        # Use throttler to enforce rate limit
        async with throttler:
            pass
    
    def get_current_rate(self, service: str) -> int:
        """Get current requests per minute for a service."""
        if service not in self.call_history:
            return 0
        
        current_time = time.time()
        cutoff_time = current_time - 60
        
        recent_calls = [
            t for t in self.call_history[service] if t > cutoff_time
        ]
        
        return len(recent_calls)
    
    def can_make_request(self, service: str, requests_per_minute: int) -> bool:
        """Check if we can make a request without hitting rate limit."""
        current_rate = self.get_current_rate(service)
        return current_rate < requests_per_minute
    
    async def wait_for_capacity(self, service: str, requests_per_minute: int):
        """Wait until we have capacity to make a request."""
        while not self.can_make_request(service, requests_per_minute):
            await asyncio.sleep(1)


class BudgetTracker:
    """Track API costs to stay within budget."""
    
    def __init__(self, max_budget: float):
        self.max_budget = max_budget
        self.current_cost = 0.0
        self.cost_by_service: Dict[str, float] = {}
    
    def add_cost(self, service: str, cost: float):
        """Add cost for a service."""
        self.current_cost += cost
        if service not in self.cost_by_service:
            self.cost_by_service[service] = 0.0
        self.cost_by_service[service] += cost
    
    def can_afford(self, estimated_cost: float) -> bool:
        """Check if we can afford an operation."""
        return (self.current_cost + estimated_cost) <= self.max_budget
    
    def get_remaining_budget(self) -> float:
        """Get remaining budget."""
        return max(0.0, self.max_budget - self.current_cost)
    
    def get_budget_usage_percentage(self) -> float:
        """Get budget usage as percentage."""
        return (self.current_cost / self.max_budget) * 100
    
    def is_budget_exceeded(self) -> bool:
        """Check if budget is exceeded."""
        return self.current_cost > self.max_budget
    
    def get_cost_breakdown(self) -> Dict[str, float]:
        """Get cost breakdown by service."""
        return self.cost_by_service.copy()


# Global instances
rate_limiter = RateLimiter()
budget_tracker = BudgetTracker(100.0)  # Will be updated from config

================
File: src/__init__.py
================
# AI Code Evaluation Framework
__version__ = "0.1.0"

================
File: src/config.py
================
"""Configuration management for the evaluation framework."""

import os
from pathlib import Path
from typing import Optional
from pydantic import BaseSettings, Field
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config(BaseSettings):
    """Configuration settings for the evaluation framework."""
    
    # API Keys
    anthropic_api_key: str = Field(..., env="ANTHROPIC_API_KEY")
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    e2b_api_key: str = Field(..., env="E2B_API_KEY")
    
    # Modal Configuration
    modal_token_id: Optional[str] = Field(None, env="MODAL_TOKEN_ID")
    modal_token_secret: Optional[str] = Field(None, env="MODAL_TOKEN_SECRET")
    
    # Evaluation Configuration
    max_budget: float = Field(100.0, env="MAX_BUDGET")
    default_workers: int = Field(5, env="DEFAULT_WORKERS")
    default_examples: int = Field(20, env="DEFAULT_EXAMPLES")
    
    # Paths
    swe_bench_dataset_path: Path = Field(Path("./data/swe_bench"), env="SWE_BENCH_DATASET_PATH")
    results_output_path: Path = Field(Path("./results"), env="RESULTS_OUTPUT_PATH")
    
    # Logging
    log_level: str = Field("INFO", env="LOG_LEVEL")
    log_file: Path = Field(Path("./logs/evaluation.log"), env="LOG_FILE")
    
    # Rate Limiting
    claude_requests_per_minute: int = 50
    openai_requests_per_minute: int = 60
    
    # Timeouts
    task_timeout_seconds: int = 300  # 5 minutes per task
    sandbox_timeout_seconds: int = 180  # 3 minutes for sandbox operations
    
    class Config:
        env_file = ".env"
        case_sensitive = False

# Global config instance
config = Config()

def ensure_directories():
    """Ensure required directories exist."""
    config.swe_bench_dataset_path.mkdir(parents=True, exist_ok=True)
    config.results_output_path.mkdir(parents=True, exist_ok=True)
    config.log_file.parent.mkdir(parents=True, exist_ok=True)

# Create directories on import
ensure_directories()

================
File: src/evaluator.py
================
"""Main evaluation orchestrator for SWE-bench tasks."""

import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime

from .models import (
    SWEBenchTask, 
    EvaluationResult, 
    EvaluationSummary,
    ComparisonReport,
    EvaluatorType,
    TaskStatus
)
from .evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
from .swe_bench import SWEBenchLoader, TestRunner, ResultValidator
from .utils import logger, eval_logger, budget_tracker
from .config import config


class SWEBenchEvaluator:
    """Main orchestrator for SWE-bench evaluations."""
    
    def __init__(self):
        self.task_loader = SWEBenchLoader()
        self.test_runner = TestRunner(use_e2b=True)  # Prefer E2B over Docker
        self.result_validator = ResultValidator()
        
        # Initialize evaluators
        self.evaluators = {
            EvaluatorType.CLAUDE: ClaudeEvaluator(),
            EvaluatorType.CODEX: CodexEvaluator(),
            EvaluatorType.CODEGEN: CodeGenEvaluator()
        }
        
        # Update budget tracker with config
        budget_tracker.max_budget = config.max_budget
    
    async def evaluate_single_task(self, 
                                 task: SWEBenchTask,
                                 evaluator_type: EvaluatorType) -> EvaluationResult:
        """Evaluate a single task with a specific evaluator.
        
        Args:
            task: SWE-bench task to evaluate
            evaluator_type: Type of evaluator to use
            
        Returns:
            Evaluation result
        """
        
        evaluator = self.evaluators[evaluator_type]
        
        try:
            # Generate solution using the evaluator
            result = await evaluator.evaluate_task(task)
            
            # If solution was generated successfully, run tests
            if result.status == TaskStatus.COMPLETED and result.solution:
                # Run tests to validate the solution
                validation_results = await self.test_runner.validate_solution(
                    task, result.solution
                )
                
                # Update result with test information
                result.test_results = validation_results.get("test_results", [])
                result.tests_passed = validation_results.get("passed_tests", 0)
                result.tests_failed = validation_results.get("failed_tests", 0)
                
                # Log final result
                eval_logger.task_completed(
                    task.instance_id,
                    evaluator_type.value,
                    result.success_rate,
                    result.execution_time,
                    result.api_cost
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Task evaluation failed", 
                        task_id=task.instance_id,
                        evaluator_type=evaluator_type.value,
                        error=str(e))
            
            # Return failed result
            return EvaluationResult(
                task_id=task.instance_id,
                evaluator_type=evaluator_type,
                status=TaskStatus.FAILED,
                error_message=str(e)
            )
    
    async def evaluate_tasks_batch(self,
                                 tasks: List[SWEBenchTask],
                                 evaluator_type: EvaluatorType,
                                 max_concurrent: int = 3) -> List[EvaluationResult]:
        """Evaluate multiple tasks with a single evaluator.
        
        Args:
            tasks: List of tasks to evaluate
            evaluator_type: Type of evaluator to use
            max_concurrent: Maximum concurrent evaluations
            
        Returns:
            List of evaluation results
        """
        
        logger.info(f"Starting batch evaluation", 
                   evaluator_type=evaluator_type.value,
                   num_tasks=len(tasks),
                   max_concurrent=max_concurrent)
        
        # Check budget before starting
        evaluator = self.evaluators[evaluator_type]
        total_estimated_cost = sum(evaluator.estimate_cost(task) for task in tasks)
        
        if not budget_tracker.can_afford(total_estimated_cost):
            raise Exception(f"Insufficient budget. Need ${total_estimated_cost:.2f}, "
                          f"have ${budget_tracker.get_remaining_budget():.2f}")
        
        # Create semaphore for concurrency control
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def evaluate_with_semaphore(task: SWEBenchTask) -> EvaluationResult:
            async with semaphore:
                # Check budget before each task
                if budget_tracker.is_budget_exceeded():
                    return EvaluationResult(
                        task_id=task.instance_id,
                        evaluator_type=evaluator_type,
                        status=TaskStatus.FAILED,
                        error_message="Budget exceeded"
                    )
                
                return await self.evaluate_single_task(task, evaluator_type)
        
        # Execute all tasks concurrently
        results = await asyncio.gather(
            *[evaluate_with_semaphore(task) for task in tasks],
            return_exceptions=True
        )
        
        # Handle exceptions
        final_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed_result = EvaluationResult(
                    task_id=tasks[i].instance_id,
                    evaluator_type=evaluator_type,
                    status=TaskStatus.FAILED,
                    error_message=str(result)
                )
                final_results.append(failed_result)
            else:
                final_results.append(result)
        
        logger.info(f"Batch evaluation completed", 
                   evaluator_type=evaluator_type.value,
                   total_tasks=len(final_results),
                   completed_tasks=sum(1 for r in final_results if r.status == TaskStatus.COMPLETED),
                   failed_tasks=sum(1 for r in final_results if r.status == TaskStatus.FAILED))
        
        return final_results
    
    async def compare_evaluators(self,
                               tasks: List[SWEBenchTask],
                               evaluator_types: List[EvaluatorType],
                               max_concurrent: int = 2) -> ComparisonReport:
        """Compare multiple evaluators on the same set of tasks.
        
        Args:
            tasks: List of tasks to evaluate
            evaluator_types: List of evaluator types to compare
            max_concurrent: Maximum concurrent evaluations per evaluator
            
        Returns:
            Comparison report
        """
        
        logger.info(f"Starting evaluator comparison", 
                   evaluator_types=[e.value for e in evaluator_types],
                   num_tasks=len(tasks))
        
        summaries = {}
        
        for evaluator_type in evaluator_types:
            try:
                logger.info(f"Evaluating with {evaluator_type.value}")
                
                # Evaluate tasks with this evaluator
                results = await self.evaluate_tasks_batch(
                    tasks, evaluator_type, max_concurrent
                )
                
                # Create summary
                summary = self.result_validator.create_evaluation_summary(
                    results, evaluator_type
                )
                summaries[evaluator_type] = summary
                
                # Save individual results
                self.result_validator.save_results(
                    results, 
                    f"results_{evaluator_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                
                # Log summary
                eval_logger.evaluation_summary(
                    evaluator_type.value,
                    {
                        "completion_rate": summary.completion_rate,
                        "success_rate": summary.overall_success_rate,
                        "total_cost": summary.total_api_cost,
                        "avg_time": summary.average_execution_time
                    }
                )
                
            except Exception as e:
                logger.error(f"Evaluator comparison failed", 
                           evaluator_type=evaluator_type.value,
                           error=str(e))
                continue
        
        # Create comparison report
        report = self.result_validator.create_comparison_report(summaries)
        
        # Save comparison report
        report_path = self.result_validator.save_comparison_report(report)
        
        # Generate and log summary
        summary_text = self.result_validator.generate_report_summary(report)
        logger.info(f"Comparison completed", report_path=report_path)
        print("\n" + summary_text)
        
        return report
    
    async def run_evaluation(self,
                           subset: str = "lite",
                           max_tasks: int = 20,
                           evaluator_types: Optional[List[EvaluatorType]] = None,
                           max_concurrent: int = 3,
                           filter_repos: Optional[List[str]] = None) -> ComparisonReport:
        """Run complete evaluation pipeline.
        
        Args:
            subset: SWE-bench subset to use ("lite", "verified", "full")
            max_tasks: Maximum number of tasks to evaluate
            evaluator_types: List of evaluators to compare (default: all)
            max_concurrent: Maximum concurrent evaluations
            filter_repos: Optional list of repositories to filter by
            
        Returns:
            Comparison report
        """
        
        if evaluator_types is None:
            evaluator_types = [EvaluatorType.CLAUDE, EvaluatorType.CODEX]
        
        logger.info(f"Starting SWE-bench evaluation", 
                   subset=subset,
                   max_tasks=max_tasks,
                   evaluator_types=[e.value for e in evaluator_types],
                   max_concurrent=max_concurrent)
        
        # Load tasks
        tasks = await self.task_loader.load_tasks(
            subset=subset,
            max_tasks=max_tasks,
            filter_repos=filter_repos
        )
        
        if not tasks:
            raise Exception("No tasks loaded")
        
        # Log task statistics
        stats = self.task_loader.get_task_statistics(tasks)
        logger.info(f"Task statistics", **stats)
        
        # Run comparison
        report = await self.compare_evaluators(
            tasks, evaluator_types, max_concurrent
        )
        
        # Log final budget usage
        budget_usage = budget_tracker.get_budget_usage_percentage()
        cost_breakdown = budget_tracker.get_cost_breakdown()
        
        logger.info(f"Evaluation completed", 
                   budget_usage_percent=budget_usage,
                   total_cost=budget_tracker.current_cost,
                   cost_breakdown=cost_breakdown)
        
        if budget_usage > 90:
            eval_logger.budget_warning(
                budget_tracker.current_cost,
                budget_tracker.max_budget
            )
        
        return report

================
File: src/models.py
================
"""Data models for the evaluation framework."""

from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """Status of a task evaluation."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"


class EvaluatorType(str, Enum):
    """Types of AI evaluators."""
    CLAUDE = "claude"
    CODEX = "codex"
    CODEGEN = "codegen"


class SWEBenchTask(BaseModel):
    """SWE-bench task data structure."""
    instance_id: str
    repo: str
    base_commit: str
    problem_statement: str
    hints_text: str = ""
    created_at: str
    version: str
    FAIL_TO_PASS: List[str] = Field(default_factory=list)
    PASS_TO_PASS: List[str] = Field(default_factory=list)
    environment_setup_commit: str = ""
    patch: str = ""
    test_patch: str = ""


class CodeSolution(BaseModel):
    """Generated code solution."""
    code: str
    explanation: str = ""
    files_modified: List[str] = Field(default_factory=list)
    confidence_score: Optional[float] = None


class TestResult(BaseModel):
    """Test execution result."""
    test_name: str
    status: str  # "PASSED", "FAILED", "ERROR"
    output: str = ""
    execution_time: float = 0.0
    error_message: Optional[str] = None


class EvaluationResult(BaseModel):
    """Result of evaluating a single task."""
    task_id: str
    evaluator_type: EvaluatorType
    status: TaskStatus
    solution: Optional[CodeSolution] = None
    test_results: List[TestResult] = Field(default_factory=list)
    
    # Metrics
    tests_passed: int = 0
    tests_failed: int = 0
    execution_time: float = 0.0
    api_cost: float = 0.0
    
    # Metadata
    started_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of tests."""
        total_tests = self.tests_passed + self.tests_failed
        if total_tests == 0:
            return 0.0
        return self.tests_passed / total_tests


class EvaluationSummary(BaseModel):
    """Summary of evaluation results across multiple tasks."""
    evaluator_type: EvaluatorType
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    
    # Aggregate metrics
    total_tests_passed: int = 0
    total_tests_failed: int = 0
    total_execution_time: float = 0.0
    total_api_cost: float = 0.0
    
    # Task results
    results: List[EvaluationResult] = Field(default_factory=list)
    
    @property
    def completion_rate(self) -> float:
        """Calculate task completion rate."""
        if self.total_tasks == 0:
            return 0.0
        return self.completed_tasks / self.total_tasks
    
    @property
    def overall_success_rate(self) -> float:
        """Calculate overall test success rate."""
        total_tests = self.total_tests_passed + self.total_tests_failed
        if total_tests == 0:
            return 0.0
        return self.total_tests_passed / total_tests
    
    @property
    def average_execution_time(self) -> float:
        """Calculate average execution time per task."""
        if self.completed_tasks == 0:
            return 0.0
        return self.total_execution_time / self.completed_tasks


class ComparisonReport(BaseModel):
    """Comparison report across multiple evaluators."""
    summaries: Dict[EvaluatorType, EvaluationSummary]
    generated_at: datetime = Field(default_factory=datetime.now)
    
    def get_best_performer(self, metric: str = "success_rate") -> Optional[EvaluatorType]:
        """Get the best performing evaluator by specified metric."""
        if not self.summaries:
            return None
        
        best_evaluator = None
        best_score = -1
        
        for evaluator_type, summary in self.summaries.items():
            if metric == "success_rate":
                score = summary.overall_success_rate
            elif metric == "completion_rate":
                score = summary.completion_rate
            elif metric == "cost_efficiency":
                # Lower cost per successful test is better
                if summary.total_tests_passed > 0:
                    score = 1 / (summary.total_api_cost / summary.total_tests_passed)
                else:
                    score = 0
            else:
                continue
            
            if score > best_score:
                best_score = score
                best_evaluator = evaluator_type
        
        return best_evaluator

================
File: tests/__init__.py
================
# Tests package

================
File: tests/test_evaluators.py
================
"""Tests for AI evaluators."""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock

from src.evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
from src.models import SWEBenchTask, EvaluatorType, TaskStatus


@pytest.fixture
def sample_task():
    """Create a sample SWE-bench task for testing."""
    return SWEBenchTask(
        instance_id="test_task_1",
        repo="test/repo",
        base_commit="abc123",
        problem_statement="Fix the bug in the function",
        hints_text="Look at the error handling",
        created_at="2024-01-01",
        version="1.0",
        FAIL_TO_PASS=["test_function_fix"],
        PASS_TO_PASS=["test_existing_functionality"]
    )


class TestClaudeEvaluator:
    """Test Claude evaluator."""
    
    @pytest.fixture
    def claude_evaluator(self):
        """Create Claude evaluator instance."""
        with patch('src.evaluators.claude_evaluator.anthropic.Anthropic'):
            return ClaudeEvaluator()
    
    def test_initialization(self, claude_evaluator):
        """Test Claude evaluator initialization."""
        assert claude_evaluator.evaluator_type == EvaluatorType.CLAUDE
        assert claude_evaluator.model == "claude-3-5-sonnet-20241022"
    
    def test_rate_limit(self, claude_evaluator):
        """Test rate limit configuration."""
        assert claude_evaluator.get_rate_limit() > 0
    
    def test_cost_estimation(self, claude_evaluator, sample_task):
        """Test cost estimation."""
        cost = claude_evaluator.estimate_cost(sample_task)
        assert cost > 0
        assert cost < 1.0  # Should be reasonable for a single task
    
    @pytest.mark.asyncio
    async def test_generate_solution_success(self, claude_evaluator, sample_task):
        """Test successful solution generation."""
        # Mock the API response
        mock_response = Mock()
        mock_response.content = [Mock()]
        mock_response.content[0].text = """
EXPLANATION:
This fixes the bug by adding proper error handling.

CODE:
def fixed_function():
    try:
        return process_data()
    except ValueError:
        return None

FILES_MODIFIED:
src/main.py
"""
        
        with patch.object(claude_evaluator, '_make_api_call', return_value=mock_response.content[0].text):
            solution = await claude_evaluator.generate_solution(sample_task)
            
            assert solution.code
            assert solution.explanation
            assert "src/main.py" in solution.files_modified
    
    @pytest.mark.asyncio
    async def test_generate_solution_api_error(self, claude_evaluator, sample_task):
        """Test solution generation with API error."""
        with patch.object(claude_evaluator, '_make_api_call', side_effect=Exception("API Error")):
            with pytest.raises(Exception, match="Claude API call failed"):
                await claude_evaluator.generate_solution(sample_task)


class TestCodexEvaluator:
    """Test Codex evaluator."""
    
    @pytest.fixture
    def codex_evaluator(self):
        """Create Codex evaluator instance."""
        return CodexEvaluator()
    
    def test_initialization(self, codex_evaluator):
        """Test Codex evaluator initialization."""
        assert codex_evaluator.evaluator_type == EvaluatorType.CODEX
        assert codex_evaluator.cli_command == "codex"
    
    def test_cost_estimation(self, codex_evaluator, sample_task):
        """Test cost estimation."""
        cost = codex_evaluator.estimate_cost(sample_task)
        assert cost > 0
    
    @pytest.mark.asyncio
    async def test_check_codex_availability_success(self, codex_evaluator):
        """Test Codex CLI availability check."""
        mock_process = Mock()
        mock_process.returncode = 0
        mock_process.communicate = AsyncMock(return_value=(b"", b""))
        
        with patch('asyncio.create_subprocess_exec', return_value=mock_process):
            # Should not raise exception
            await codex_evaluator._check_codex_availability()
    
    @pytest.mark.asyncio
    async def test_check_codex_availability_not_found(self, codex_evaluator):
        """Test Codex CLI not found."""
        with patch('asyncio.create_subprocess_exec', side_effect=FileNotFoundError()):
            with pytest.raises(Exception, match="Codex CLI not installed"):
                await codex_evaluator._check_codex_availability()


class TestCodeGenEvaluator:
    """Test CodeGen evaluator."""
    
    @pytest.fixture
    def codegen_evaluator(self):
        """Create CodeGen evaluator instance."""
        return CodeGenEvaluator()
    
    def test_initialization(self, codegen_evaluator):
        """Test CodeGen evaluator initialization."""
        assert codegen_evaluator.evaluator_type == EvaluatorType.CODEGEN
        assert codegen_evaluator.api_endpoint
    
    def test_cost_estimation(self, codegen_evaluator, sample_task):
        """Test cost estimation."""
        cost = codegen_evaluator.estimate_cost(sample_task)
        assert cost > 0
    
    def test_create_request_payload(self, codegen_evaluator, sample_task):
        """Test request payload creation."""
        payload = codegen_evaluator._create_request_payload(sample_task)
        
        assert "prompt" in payload
        assert sample_task.problem_statement in payload["prompt"]
        assert payload["max_tokens"] > 0
        assert payload["temperature"] >= 0
    
    def test_parse_response_openai_style(self, codegen_evaluator):
        """Test parsing OpenAI-style response."""
        response_data = {
            "choices": [
                {
                    "text": "def solution():\n    return 'fixed'"
                }
            ]
        }
        
        solution = codegen_evaluator._parse_response(response_data)
        assert solution.code
        assert "def solution" in solution.code
    
    def test_parse_response_direct_code(self, codegen_evaluator):
        """Test parsing direct code response."""
        response_data = {
            "generated_code": "def solution():\n    return 'fixed'"
        }
        
        solution = codegen_evaluator._parse_response(response_data)
        assert solution.code
        assert "def solution" in solution.code


@pytest.mark.asyncio
async def test_base_evaluator_batch_evaluate():
    """Test batch evaluation functionality."""
    from src.evaluators.base_evaluator import BaseEvaluator
    from src.models import CodeSolution
    
    class MockEvaluator(BaseEvaluator):
        def __init__(self):
            super().__init__(EvaluatorType.CLAUDE)
        
        async def generate_solution(self, task):
            return CodeSolution(
                code="def mock_solution(): pass",
                explanation="Mock solution"
            )
        
        def estimate_cost(self, task):
            return 0.01
        
        def get_rate_limit(self):
            return 60
    
    evaluator = MockEvaluator()
    tasks = [
        SWEBenchTask(
            instance_id=f"task_{i}",
            repo="test/repo",
            base_commit="abc123",
            problem_statement=f"Problem {i}",
            hints_text="",
            created_at="2024-01-01",
            version="1.0"
        )
        for i in range(3)
    ]
    
    results = await evaluator.batch_evaluate(tasks, max_concurrent=2)
    
    assert len(results) == 3
    for result in results:
        assert result.status == TaskStatus.COMPLETED
        assert result.solution is not None


if __name__ == "__main__":
    pytest.main([__file__])

================
File: tests/test_swe_bench.py
================
"""Tests for SWE-bench integration."""

import pytest
import json
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock

from src.swe_bench import SWEBenchLoader, TestRunner, ResultValidator
from src.models import SWEBenchTask, CodeSolution, EvaluationResult, EvaluatorType, TaskStatus


@pytest.fixture
def sample_swe_bench_data():
    """Sample SWE-bench data for testing."""
    return [
        {
            "instance_id": "test_task_1",
            "repo": "test/repo",
            "base_commit": "abc123",
            "problem_statement": "Fix the bug in function X",
            "hints_text": "Check error handling",
            "created_at": "2024-01-01",
            "version": "1.0",
            "FAIL_TO_PASS": ["test_function_x"],
            "PASS_TO_PASS": ["test_existing_feature"]
        },
        {
            "instance_id": "test_task_2", 
            "repo": "test/repo2",
            "base_commit": "def456",
            "problem_statement": "Improve performance of function Y",
            "hints_text": "",
            "created_at": "2024-01-02",
            "version": "1.0",
            "FAIL_TO_PASS": ["test_performance"],
            "PASS_TO_PASS": []
        }
    ]


class TestSWEBenchLoader:
    """Test SWE-bench task loader."""
    
    @pytest.fixture
    def loader(self):
        """Create SWE-bench loader instance."""
        return SWEBenchLoader()
    
    def test_initialization(self, loader):
        """Test loader initialization."""
        assert loader.dataset_path.exists()
        assert loader.cache_file.name == "swe_bench_cache.json"
    
    def test_parse_task_data(self, loader, sample_swe_bench_data):
        """Test parsing task data."""
        task_data = sample_swe_bench_data[0]
        task = loader._parse_task_data(task_data)
        
        assert isinstance(task, SWEBenchTask)
        assert task.instance_id == "test_task_1"
        assert task.repo == "test/repo"
        assert task.problem_statement == "Fix the bug in function X"
        assert "test_function_x" in task.FAIL_TO_PASS
    
    def test_parse_task_data_missing_required_field(self, loader):
        """Test parsing with missing required field."""
        invalid_data = {"repo": "test/repo"}  # Missing instance_id
        
        with pytest.raises(ValueError, match="Missing instance_id"):
            loader._parse_task_data(invalid_data)
    
    @pytest.mark.asyncio
    async def test_load_from_cache(self, loader, sample_swe_bench_data):
        """Test loading tasks from cache."""
        # Create temporary cache file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            cache_data = {"lite": sample_swe_bench_data}
            json.dump(cache_data, f)
            cache_file = Path(f.name)
        
        # Mock the cache file path
        loader.cache_file = cache_file
        
        try:
            tasks = await loader._load_from_cache("lite")
            assert len(tasks) == 2
            assert all(isinstance(task, SWEBenchTask) for task in tasks)
        finally:
            cache_file.unlink()
    
    @pytest.mark.asyncio
    async def test_download_dataset(self, loader, sample_swe_bench_data):
        """Test downloading dataset."""
        mock_response = Mock()
        mock_response.json.return_value = sample_swe_bench_data
        mock_response.raise_for_status.return_value = None
        
        with patch('requests.get', return_value=mock_response):
            tasks = await loader._download_dataset("lite")
            
            assert len(tasks) == 2
            assert all(isinstance(task, SWEBenchTask) for task in tasks)
    
    def test_get_task_statistics(self, loader, sample_swe_bench_data):
        """Test task statistics calculation."""
        tasks = [loader._parse_task_data(data) for data in sample_swe_bench_data]
        stats = loader.get_task_statistics(tasks)
        
        assert stats["total_tasks"] == 2
        assert stats["unique_repos"] == 2
        assert "repo_distribution" in stats
        assert stats["tasks_with_tests"] == 2


class TestTestRunner:
    """Test test runner."""
    
    @pytest.fixture
    def test_runner(self):
        """Create test runner instance."""
        with patch('src.swe_bench.test_runner.E2BSandboxManager'):
            return TestRunner(use_e2b=True)
    
    @pytest.fixture
    def sample_task(self):
        """Create sample task."""
        return SWEBenchTask(
            instance_id="test_task",
            repo="test/repo",
            base_commit="abc123",
            problem_statement="Fix bug",
            hints_text="",
            created_at="2024-01-01",
            version="1.0",
            FAIL_TO_PASS=["test_bug_fix"],
            PASS_TO_PASS=["test_existing"]
        )
    
    @pytest.fixture
    def sample_solution(self):
        """Create sample solution."""
        return CodeSolution(
            code="def fixed_function(): return True",
            explanation="Fixed the bug",
            files_modified=["src/main.py"]
        )
    
    def test_initialization(self, test_runner):
        """Test test runner initialization."""
        assert test_runner.use_e2b is True
        assert test_runner.sandbox_manager is not None
    
    def test_is_test_result_correct_fail_to_pass(self, test_runner, sample_task):
        """Test correctness evaluation for FAIL_TO_PASS tests."""
        from src.models import TestResult
        
        # Test should fail in baseline and pass after solution
        baseline_result = TestResult(test_name="test_bug_fix", status="FAILED", output="")
        solution_result = TestResult(test_name="test_bug_fix", status="PASSED", output="")
        
        is_correct = test_runner._is_test_result_correct(
            sample_task, "test_bug_fix", baseline_result, solution_result
        )
        
        assert is_correct is True
    
    def test_is_test_result_correct_pass_to_pass(self, test_runner, sample_task):
        """Test correctness evaluation for PASS_TO_PASS tests."""
        from src.models import TestResult
        
        # Test should pass in both baseline and after solution
        baseline_result = TestResult(test_name="test_existing", status="PASSED", output="")
        solution_result = TestResult(test_name="test_existing", status="PASSED", output="")
        
        is_correct = test_runner._is_test_result_correct(
            sample_task, "test_existing", baseline_result, solution_result
        )
        
        assert is_correct is True
    
    @pytest.mark.asyncio
    async def test_validate_solution(self, test_runner, sample_task, sample_solution):
        """Test solution validation."""
        from src.models import TestResult
        
        # Mock test results
        mock_test_results = [
            TestResult(test_name="test_bug_fix", status="PASSED", output=""),
            TestResult(test_name="test_existing", status="PASSED", output="")
        ]
        
        with patch.object(test_runner, 'run_task_tests', return_value=mock_test_results):
            validation_result = await test_runner.validate_solution(sample_task, sample_solution)
            
            assert validation_result["overall_success"] is True
            assert validation_result["total_tests"] == 2
            assert validation_result["passed_tests"] == 2
            assert validation_result["success_rate"] == 1.0


class TestResultValidator:
    """Test result validator."""
    
    @pytest.fixture
    def validator(self):
        """Create result validator instance."""
        return ResultValidator()
    
    @pytest.fixture
    def sample_result(self):
        """Create sample evaluation result."""
        from src.models import TestResult
        
        return EvaluationResult(
            task_id="test_task",
            evaluator_type=EvaluatorType.CLAUDE,
            status=TaskStatus.COMPLETED,
            solution=CodeSolution(
                code="def solution(): pass",
                explanation="Test solution"
            ),
            test_results=[
                TestResult(
                    test_name="test_1",
                    status="PASSED",
                    output="[EVALUATION] Test correctness: CORRECT"
                ),
                TestResult(
                    test_name="test_2", 
                    status="FAILED",
                    output="[EVALUATION] Test correctness: INCORRECT"
                )
            ],
            tests_passed=1,
            tests_failed=1,
            execution_time=10.0,
            api_cost=0.05
        )
    
    def test_validate_evaluation_result(self, validator, sample_result):
        """Test evaluation result validation."""
        validation_report = validator.validate_evaluation_result(sample_result)
        
        assert validation_report["is_valid"] is True
        assert validation_report["task_id"] == "test_task"
        assert "metrics" in validation_report
        assert validation_report["metrics"]["total_tests"] == 2
        assert validation_report["metrics"]["passed_tests"] == 1
    
    def test_calculate_swe_bench_metrics(self, validator, sample_result):
        """Test SWE-bench metrics calculation."""
        metrics = validator._calculate_swe_bench_metrics(sample_result)
        
        assert metrics["total_tests"] == 2
        assert metrics["passed_tests"] == 1
        assert metrics["failed_tests"] == 1
        assert metrics["correct_tests"] == 1
        assert metrics["pass_rate"] == 0.5
        assert metrics["correctness_rate"] == 0.5
        assert metrics["swe_bench_success"] is False  # Not all tests correct
    
    def test_create_evaluation_summary(self, validator):
        """Test evaluation summary creation."""
        from src.models import TestResult
        
        results = [
            EvaluationResult(
                task_id="task_1",
                evaluator_type=EvaluatorType.CLAUDE,
                status=TaskStatus.COMPLETED,
                test_results=[TestResult(test_name="test", status="PASSED", output="")],
                tests_passed=1,
                tests_failed=0,
                execution_time=5.0,
                api_cost=0.02
            ),
            EvaluationResult(
                task_id="task_2",
                evaluator_type=EvaluatorType.CLAUDE,
                status=TaskStatus.FAILED,
                error_message="API error"
            )
        ]
        
        summary = validator.create_evaluation_summary(results, EvaluatorType.CLAUDE)
        
        assert summary.evaluator_type == EvaluatorType.CLAUDE
        assert summary.total_tasks == 2
        assert summary.completed_tasks == 1
        assert summary.failed_tasks == 1
        assert summary.total_tests_passed == 1
        assert summary.total_api_cost == 0.02
    
    def test_save_and_load_results(self, validator, sample_result):
        """Test saving and loading results."""
        results = [sample_result]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            validator.results_path = Path(temp_dir)
            
            # Save results
            filepath = validator.save_results(results, "test_results.json")
            assert Path(filepath).exists()
            
            # Load results
            loaded_results = validator.load_results(filepath)
            assert len(loaded_results) == 1
            assert loaded_results[0].task_id == sample_result.task_id


if __name__ == "__main__":
    pytest.main([__file__])

================
File: .env.example
================
# API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
E2B_API_KEY=your_e2b_api_key_here

# Modal Configuration
MODAL_TOKEN_ID=your_modal_token_id_here
MODAL_TOKEN_SECRET=your_modal_token_secret_here

# Evaluation Configuration
MAX_BUDGET=100.0
DEFAULT_WORKERS=5
DEFAULT_EXAMPLES=20

# SWE-bench Configuration
SWE_BENCH_DATASET_PATH=./data/swe_bench
RESULTS_OUTPUT_PATH=./results

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/evaluation.log

================
File: ARCHITECTURE.md
================
# Architecture Documentation

This document describes the architecture and design decisions of the SWE-bench AI Evaluator.

## Overview

The SWE-bench AI Evaluator is designed as a modular, scalable framework for comparing AI coding tools on real-world software engineering tasks. It follows the SWE-bench evaluation methodology using deterministic test-based validation.

## Core Principles

1. **Deterministic Evaluation**: Use actual test execution rather than LLM-as-judge
2. **Modular Design**: Easy to add new AI evaluators and sandbox providers
3. **Cost Awareness**: Built-in budget tracking and rate limiting
4. **Scalability**: Support for both local and cloud-based parallel execution
5. **Reproducibility**: Comprehensive logging and result serialization

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     CLI Interface                           │
│                   (cli.py)                                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Main Evaluator                               │
│              (src/evaluator.py)                             │
│  • Orchestrates evaluation pipeline                        │
│  • Manages concurrent execution                             │
│  • Coordinates components                                   │
└─────┬───────────────┬───────────────┬─────────────────────┬─┘
      │               │               │                     │
┌─────▼─────┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌─────────▼─────┐
│Evaluators │  │SWE-bench  │  │ Sandbox   │  │   Utilities   │
│           │  │Integration│  │ Managers  │  │               │
│• Claude   │  │           │  │           │  │• Logging      │
│• Codex    │  │• Loader   │  │• E2B      │  │• Rate Limit   │
│• CodeGen  │  │• Runner   │  │• Docker   │  │• Budget Track │
│           │  │• Validator│  │           │  │• Config       │
└───────────┘  └───────────┘  └───────────┘  └───────────────┘
```

## Component Details

### 1. Evaluators (`src/evaluators/`)

**Purpose**: Interface with different AI coding tools

**Components**:
- `BaseEvaluator`: Abstract base class defining the evaluator interface
- `ClaudeEvaluator`: Anthropic Claude API integration
- `CodexEvaluator`: OpenAI Codex CLI integration  
- `CodeGenEvaluator`: CodeGen API integration

**Key Features**:
- Async/await for non-blocking API calls
- Built-in rate limiting and cost estimation
- Standardized solution format
- Error handling and retry logic

**Design Pattern**: Strategy pattern for interchangeable AI evaluators

### 2. SWE-bench Integration (`src/swe_bench/`)

**Purpose**: Handle SWE-bench dataset and evaluation logic

**Components**:
- `SWEBenchLoader`: Download, cache, and parse SWE-bench tasks
- `TestRunner`: Execute tests in sandboxed environments
- `ResultValidator`: Validate results against SWE-bench criteria

**Key Features**:
- Automatic dataset downloading and caching
- Deterministic test evaluation (FAIL_TO_PASS, PASS_TO_PASS)
- Comprehensive result validation
- Support for different dataset subsets

### 3. Sandbox Managers (`src/sandbox/`)

**Purpose**: Provide secure, isolated code execution environments

**Components**:
- `E2BSandboxManager`: E2B cloud sandbox integration
- `DockerSandboxManager`: Local Docker container management

**Key Features**:
- Repository cloning and setup
- Dependency installation
- Code application and test execution
- Automatic cleanup

**Design Pattern**: Strategy pattern with fallback (E2B → Docker)

### 4. Utilities (`src/utils/`)

**Purpose**: Cross-cutting concerns and shared functionality

**Components**:
- `logger.py`: Structured logging with evaluation-specific events
- `rate_limiter.py`: API rate limiting and budget tracking
- `config.py`: Configuration management with environment variables

## Data Models (`src/models.py`)

The system uses Pydantic models for type safety and validation:

```python
SWEBenchTask → CodeSolution → TestResult → EvaluationResult → EvaluationSummary → ComparisonReport
```

**Key Models**:
- `SWEBenchTask`: Represents a single SWE-bench evaluation task
- `CodeSolution`: AI-generated solution with metadata
- `EvaluationResult`: Complete result of evaluating one task
- `ComparisonReport`: Aggregated comparison across evaluators

## Evaluation Pipeline

### 1. Task Loading
```
SWE-bench Dataset → Cache Check → Download/Parse → Filter → Task List
```

### 2. Solution Generation
```
Task → AI Evaluator → Rate Limit → API Call → Parse Response → CodeSolution
```

### 3. Test Execution
```
Task + Solution → Sandbox → Repository Setup → Baseline Tests → Apply Solution → Final Tests → TestResults
```

### 4. Validation
```
TestResults → SWE-bench Criteria → Success/Failure → Metrics → EvaluationResult
```

### 5. Aggregation
```
EvaluationResults → Summary Statistics → Comparison → Report Generation
```

## Concurrency Model

The system uses Python's `asyncio` for concurrent execution:

- **Task Level**: Multiple tasks evaluated concurrently
- **Evaluator Level**: Different AI tools run in parallel
- **Rate Limiting**: Per-service semaphores prevent API overload
- **Resource Management**: Sandbox cleanup and error handling

```python
# Concurrency structure
Semaphore(max_concurrent_tasks)
├── Task 1 → Evaluator A → Sandbox → Tests
├── Task 2 → Evaluator B → Sandbox → Tests  
└── Task N → Evaluator C → Sandbox → Tests
```

## Configuration Management

**Hierarchy** (highest to lowest priority):
1. Command-line arguments
2. Environment variables
3. `.env` file
4. Default values

**Key Configuration Areas**:
- API credentials and endpoints
- Rate limits and timeouts
- Budget constraints
- File paths and directories
- Logging levels

## Error Handling Strategy

**Levels of Error Handling**:

1. **Task Level**: Individual task failures don't stop the evaluation
2. **Evaluator Level**: API errors are caught and logged
3. **Sandbox Level**: Cleanup always occurs, even on failure
4. **System Level**: Budget and rate limit enforcement

**Error Recovery**:
- Automatic retries for transient failures
- Graceful degradation (E2B → Docker)
- Comprehensive error logging
- Partial result preservation

## Scalability Considerations

### Local Scaling
- Configurable concurrency limits
- Memory-efficient streaming
- Incremental result saving
- Resource cleanup

### Cloud Scaling (Modal)
- Serverless function deployment
- Automatic scaling based on load
- Distributed task execution
- Cost optimization

## Security Considerations

### Sandbox Security
- Isolated execution environments
- No network access from sandboxes
- Temporary file cleanup
- Resource limits

### API Security
- Secure credential storage
- Rate limiting to prevent abuse
- Budget limits to prevent runaway costs
- Input validation and sanitization

## Performance Optimizations

### Caching
- SWE-bench dataset caching
- API response caching (where appropriate)
- Compiled regex patterns
- Configuration caching

### Resource Management
- Connection pooling for HTTP clients
- Lazy loading of large datasets
- Streaming for large files
- Memory-mapped files for results

### Parallel Processing
- Async/await throughout
- Configurable worker pools
- Load balancing across evaluators
- Batch processing optimizations

## Monitoring and Observability

### Logging
- Structured JSON logging
- Multiple log levels
- Evaluation-specific events
- Performance metrics

### Metrics
- Success rates and completion rates
- Cost tracking and budget usage
- Execution time distributions
- Error rates and types

### Debugging
- Comprehensive error messages
- Stack trace preservation
- Request/response logging
- State inspection utilities

## Extension Points

### Adding New Evaluators
1. Inherit from `BaseEvaluator`
2. Implement required methods
3. Add to evaluator factory
4. Update CLI options

### Adding New Sandbox Providers
1. Implement sandbox manager interface
2. Add to sandbox factory
3. Update configuration
4. Add fallback logic

### Adding New Metrics
1. Extend result models
2. Update validation logic
3. Modify report generation
4. Update CLI display

## Testing Strategy

### Unit Tests
- Individual component testing
- Mock external dependencies
- Edge case validation
- Error condition testing

### Integration Tests
- End-to-end pipeline testing
- Real API integration (with limits)
- Sandbox functionality
- Configuration validation

### Performance Tests
- Load testing with multiple tasks
- Memory usage profiling
- Concurrency stress testing
- Cost optimization validation

This architecture provides a solid foundation for reliable, scalable AI code evaluation while maintaining flexibility for future enhancements.

================
File: cli.py
================
"""Command-line interface for SWE-bench evaluation."""

import asyncio
import click
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from typing import List, Optional

from src.evaluator import SWEBenchEvaluator
from src.models import EvaluatorType
from src.config import config
from src.utils import logger

console = Console()


def print_banner():
    """Print application banner."""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    SWE-bench AI Evaluator                   ║
║              Compare Claude, Codex, and CodeGen             ║
╚══════════════════════════════════════════════════════════════╝
    """
    console.print(banner, style="bold blue")


@click.group()
def cli():
    """SWE-bench AI Code Evaluator - Compare different AI coding tools."""
    print_banner()


@cli.command()
@click.option('--model', 
              type=click.Choice(['claude', 'codex', 'codegen', 'all']),
              default='claude',
              help='AI model to evaluate')
@click.option('--examples', 
              type=int, 
              default=config.default_examples,
              help='Number of examples to evaluate')
@click.option('--workers', 
              type=int, 
              default=config.default_workers,
              help='Number of concurrent workers')
@click.option('--subset',
              type=click.Choice(['lite', 'verified', 'full']),
              default='lite',
              help='SWE-bench dataset subset')
@click.option('--repos',
              help='Comma-separated list of repositories to filter by')
@click.option('--budget',
              type=float,
              default=config.max_budget,
              help='Maximum budget in USD')
def evaluate(model: str, examples: int, workers: int, subset: str, 
            repos: Optional[str], budget: float):
    """Evaluate AI models on SWE-bench tasks."""
    
    # Parse repositories filter
    filter_repos = None
    if repos:
        filter_repos = [repo.strip() for repo in repos.split(',')]
    
    # Parse evaluator types
    if model == 'all':
        evaluator_types = [EvaluatorType.CLAUDE, EvaluatorType.CODEX, EvaluatorType.CODEGEN]
    else:
        evaluator_type_map = {
            'claude': EvaluatorType.CLAUDE,
            'codex': EvaluatorType.CODEX,
            'codegen': EvaluatorType.CODEGEN
        }
        evaluator_types = [evaluator_type_map[model]]
    
    # Update budget
    config.max_budget = budget
    
    console.print(f"\n[bold green]Starting Evaluation[/bold green]")
    console.print(f"Model(s): {', '.join(e.value for e in evaluator_types)}")
    console.print(f"Examples: {examples}")
    console.print(f"Workers: {workers}")
    console.print(f"Subset: {subset}")
    console.print(f"Budget: ${budget:.2f}")
    if filter_repos:
        console.print(f"Repositories: {', '.join(filter_repos)}")
    console.print()
    
    # Run evaluation
    asyncio.run(_run_evaluation(
        evaluator_types, examples, workers, subset, filter_repos
    ))


async def _run_evaluation(evaluator_types: List[EvaluatorType],
                         examples: int,
                         workers: int,
                         subset: str,
                         filter_repos: Optional[List[str]]):
    """Run the evaluation asynchronously."""
    
    evaluator = SWEBenchEvaluator()
    
    try:
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            
            # Add progress tasks
            load_task = progress.add_task("Loading SWE-bench tasks...", total=None)
            
            # Run evaluation
            report = await evaluator.run_evaluation(
                subset=subset,
                max_tasks=examples,
                evaluator_types=evaluator_types,
                max_concurrent=workers,
                filter_repos=filter_repos
            )
            
            progress.update(load_task, completed=True, description="✅ Evaluation completed!")
        
        # Display results
        _display_results(report)
        
    except Exception as e:
        console.print(f"\n[bold red]Error:[/bold red] {str(e)}")
        logger.error(f"CLI evaluation failed", error=str(e))


def _display_results(report):
    """Display evaluation results in a nice table."""
    
    console.print(f"\n[bold green]Evaluation Results[/bold green]")
    
    # Create results table
    table = Table(title="SWE-bench Evaluation Comparison")
    
    table.add_column("Model", style="cyan", no_wrap=True)
    table.add_column("Tasks", justify="right")
    table.add_column("Completed", justify="right")
    table.add_column("Success Rate", justify="right")
    table.add_column("Avg Time", justify="right")
    table.add_column("Total Cost", justify="right")
    
    for evaluator_type, summary in report.summaries.items():
        table.add_row(
            evaluator_type.value.title(),
            str(summary.total_tasks),
            f"{summary.completed_tasks}/{summary.total_tasks}",
            f"{summary.overall_success_rate:.1%}",
            f"{summary.average_execution_time:.1f}s",
            f"${summary.total_api_cost:.2f}"
        )
    
    console.print(table)
    
    # Show best performers
    console.print(f"\n[bold yellow]Best Performers:[/bold yellow]")
    
    best_success = report.get_best_performer("success_rate")
    best_completion = report.get_best_performer("completion_rate")
    best_cost = report.get_best_performer("cost_efficiency")
    
    if best_success:
        console.print(f"🏆 Highest Success Rate: [bold]{best_success.value}[/bold]")
    if best_completion:
        console.print(f"✅ Highest Completion Rate: [bold]{best_completion.value}[/bold]")
    if best_cost:
        console.print(f"💰 Most Cost Efficient: [bold]{best_cost.value}[/bold]")


@cli.command()
@click.option('--subset',
              type=click.Choice(['lite', 'verified', 'full']),
              default='lite',
              help='SWE-bench dataset subset')
@click.option('--max-tasks',
              type=int,
              default=50,
              help='Maximum tasks to show info for')
def info(subset: str, max_tasks: int):
    """Show information about SWE-bench dataset."""
    
    console.print(f"\n[bold blue]SWE-bench Dataset Information[/bold blue]")
    
    asyncio.run(_show_dataset_info(subset, max_tasks))


async def _show_dataset_info(subset: str, max_tasks: int):
    """Show dataset information."""
    
    from src.swe_bench import SWEBenchLoader
    
    loader = SWEBenchLoader()
    
    with Progress(
        SpinnerColumn(),
        TextColumn("[progress.description]{task.description}"),
        console=console
    ) as progress:
        
        load_task = progress.add_task("Loading dataset...", total=None)
        
        tasks = await loader.load_tasks(subset=subset, max_tasks=max_tasks)
        stats = loader.get_task_statistics(tasks)
        
        progress.update(load_task, completed=True, description="✅ Dataset loaded!")
    
    # Display statistics
    console.print(f"\n[bold green]Dataset Statistics ({subset})[/bold green]")
    console.print(f"Total Tasks: {stats['total_tasks']}")
    console.print(f"Unique Repositories: {stats['unique_repos']}")
    console.print(f"Average Problem Length: {stats['avg_problem_length']:.0f} characters")
    console.print(f"Tasks with Hints: {stats['tasks_with_hints']}")
    console.print(f"Tasks with Tests: {stats['tasks_with_tests']}")
    
    # Show repository distribution
    console.print(f"\n[bold yellow]Repository Distribution (Top 10):[/bold yellow]")
    
    repo_dist = stats['repo_distribution']
    sorted_repos = sorted(repo_dist.items(), key=lambda x: x[1], reverse=True)[:10]
    
    for repo, count in sorted_repos:
        console.print(f"  {repo}: {count} tasks")


@cli.command()
@click.argument('results_file')
def analyze(results_file: str):
    """Analyze saved evaluation results."""
    
    console.print(f"\n[bold blue]Analyzing Results[/bold blue]")
    console.print(f"File: {results_file}")
    
    try:
        from src.swe_bench import ResultValidator
        
        validator = ResultValidator()
        results = validator.load_results(results_file)
        
        console.print(f"\nLoaded {len(results)} results")
        
        # Show basic statistics
        completed = sum(1 for r in results if r.status.value == "completed")
        failed = sum(1 for r in results if r.status.value == "failed")
        total_cost = sum(r.api_cost for r in results)
        
        console.print(f"Completed: {completed}")
        console.print(f"Failed: {failed}")
        console.print(f"Total Cost: ${total_cost:.2f}")
        
    except Exception as e:
        console.print(f"[bold red]Error:[/bold red] {str(e)}")


@cli.command()
def setup():
    """Setup the evaluation environment."""
    
    console.print(f"\n[bold blue]Environment Setup[/bold blue]")
    
    # Check API keys
    console.print("Checking API keys...")
    
    checks = [
        ("Anthropic API Key", config.anthropic_api_key),
        ("OpenAI API Key", config.openai_api_key),
        ("E2B API Key", config.e2b_api_key)
    ]
    
    for name, key in checks:
        if key and key != "your_api_key_here":
            console.print(f"  ✅ {name}: Configured")
        else:
            console.print(f"  ❌ {name}: Missing")
    
    # Check directories
    console.print("\nChecking directories...")
    
    dirs = [
        ("Dataset Path", config.swe_bench_dataset_path),
        ("Results Path", config.results_output_path),
        ("Log Path", config.log_file.parent)
    ]
    
    for name, path in dirs:
        if path.exists():
            console.print(f"  ✅ {name}: {path}")
        else:
            console.print(f"  ❌ {name}: {path} (will be created)")
    
    console.print(f"\n[bold green]Setup complete![/bold green]")
    console.print("Make sure to set your API keys in the .env file.")


if __name__ == '__main__':
    cli()

================
File: context.md
================
Me: Once you create a feature there is no best practice to test the feature. And make sure the code actually works. So what we're doing is after you, let's say, make a feature, now you want to your terminal, do backspace run. What it will do is write the missing test for you. QA the codes for you, that that feature you wrote. K. And then productionize the code for you, like NPM run build. And then yeah, just make sure your code works in production. Once it works, make a PR and then it will you can easily merge it, making, like, vibe coded code production ready. Do you do you okay. Yeah. Yeah. So what I'm getting, right, we are wife, Corey. So how are Vive coded code works in productions? Too long there. Right? Yes. Basically, let's say you make a feature. Okay. From cursor. Right? Okay. How do you know the feature work? What do you do usually? So so I test it, run it, and check Okay. If it's working. Or I maybe try to run unit test if the feature is working. Nice. Yes. You and you and you vibe code these unit tests, right, and all that stuff? Yeah. Okay. So let's say you created one or two unit tests. And it's sort of working locally. Right? Yeah. Now you want to push and merge this vibe code. Right? Likely, if you're trying to productionize this code, you need to write more extensive tests. Also, you need to make sure your code builds properly. And your code is refactored in a modular way. Right? That's the best way to to productionize it. Otherwise, if you just push and merge, okay. Fine. You did one local test. But there could be other ways it fails. Right? And then also the code is probably bloated. Because you didn't go take the time to fix the modularity of the code. You get me? Because it's white coded. Yeah. Right? So our thing after you, you know, white code and then make one or two unit test pass, then it will run another background agent that will basically make the code more modular with best practices for, like, React Next. Js, this is that. And then it will also write missing tests that you haven't thought of. Okay. Or or it will act go and QA the code. Let's say it does NPM run dev and then goes to the the route and then uses stagehand to to get screenshots and actually click on buttons to integrate integration test. Okay. So all these are, like, background agents that will run And once they run, once the tests are done, once the code is modular, it is essentially production ready. Which is then easy for a developer to merge. Right. And then a code that is, like, vibe coded using Cloud Code, but then you are don't have the time to manually test and and, like, update everything. See. The way I see it, like, you guys are fixing VIVE coding. So that it works in productions. Exactly. Basically, it's a vibe coding framework. So it's like vibe testing. Sure. Vibe testing, you can think of it that way. A little bit more than testing. Because you'll test the code, but you also want to make sure your code is clean. So we're gonna, like, it's just essentially another clogged code run. These background agents are cloud code, by the way. These are just, like, asynchronous cloud code. That you run, and it goes and makes your code more modular. So Right? Maybe it's it's splits up, like, let's see let's see your vibe code at component. Puts everything in page dot TSX. Right? All the components and things. Likely, what our back space run will do is analyze that page dot TSX and then instead break up everything into components. And put them in the components folder. That's, like, a way of, like, cleaning up code. Right? Yeah. So it will do that and then make the test pass, and then make sure you can do NPM run build. And then your code looks good according to stagehand. I see. As a browser agent, that will go and check your code. I see. You get it? Is is agents will work as a valid validating agent. We'll validate. Yeah. It will work as a valid agent. Yeah. I see. Got it. Got the sense of what you guys are doing right. So so that's what we're doing. We're not focused We're not competing with or Devin or anything. We are trying to compliment them. So there's the software development life cycle. Right? We're trying to target the testing and deployment life cycle, essentially, part. I see. Yeah. I will send you another doc. Okay. Another document that explains all this, but basically, what we want to do is after we create finish creating our agents, we want to actually create eval datasets and then eval it. Yeah. So Right? Yeah. So eval for doing the evals, do you have output example to taste your evils? Right? For example, you can use LLM as a gel. For for now. Yeah. Yeah. So how do you create the output? Like, do you have all the tests test output? This is the thing. Is LLM as a judge the best way? So if you look at SWE bench, go to SWE bench. Yeah. What they do is how they do their evals is they have previous tests okay. So they have a GitHub issue. Right? They have a GitHub issue. And they want to implement a feature using, like, Cloud Code or like, Cursor or CodeGen. Once you implement the issue, there is a previous test that fails. Like, before you implemented the issue, And then once you implement the issue, the test must pass. That test that failed must pass. If it passed, that means the feature was implemented correctly. I see. You see? That's how they make it deterministic. Not as, like, LLM as a judge. Of the times, LLM as a judge also fails. Because it's too opinionated or, like, do you get what I mean? Yeah. Yeah. Yeah. So I think it would be best if we collect by if you guys collect the real human annotated label dataset. Right? Yes. Yeah. And as of my knowledge, so if it's failed, you can you can you can also give a reasoning model, sample. It will generate synthetic datasets. Yeah. Yeah. So using also, I think Al-Ekram is very good at generating. There's are some libraries for generating synthetic datasets. And you we we will review. Like, for example, I generated some box, right, features. Using the since LLM. Right? And there's a synthetic dataset. And I I do the classifications. So I'll I'll I'll give the meta tags. This is the this problem. This is the this problem. And with. Right? So one way to do this using the synthetic dataset the best way to do is illuminate real world datasets. You can you can collect it from GitHub. I think there are some dataset out there. For now, LLMS JAWS is good. We can also use the we can also implement this. And the best thing is to to the way I see the evils is to look at the data. Like, those custom, like, for example, they have their own matrices like but based on the problem you guys solving, we have to define. We have to make our own matrices. Like if it's passing the arrow. Right? So for example, accuracy, toxicity for REG. It doesn't work in real world. Right? So I think we have to find what is our goal in this goal. Golden datasets or golden standard. Or golden accuracy or matrices. We will implement those matrices based on the experience we have, what you guys have, and we'll also use the the traditional matrices and also custom matrices. And Okay. If I know that, what should be the ideal the most ideal without five coding? So for example, forget the vive coding. Right? How what is the most accurate way to taste We have example. For example, the previous all the unit testing and it passed. Right? We can collect those datasets. Before p l l p l l m p l l m datasets test cases. You use those as a matrices. To see how vibe coding is passing there. Right? Okay. For example, let's say your input is let's say this is your input. Right? It is a Vibe coded feature that kind of works. And then the output is that vibe coded feature that is fully production ready. Okay. Those are the inputs and the outputs. So now, how do you best verify this? How do you yeah. Basically, how do you best verify this and how do you collect this data is gonna be the task for whoever, like, takes this job. Right? So that's it. I see. Okay. So do you think does this sound interesting to you? Of course. Okay. So so if so, before I introduce you to Roland, I'm a there basically, I'm a there j I just do a take home. So that it's easier for you as well. You don't have to, like, waste your time. Yeah. Yeah. If you do a take home where you evaluate or run sweepench, on clot code, codex, maybe CodeGen and CodeLand Devon, or, like, maybe two or three. Right? I I don't have with Devin and other stuffs. Cursor plotter. Yeah. It's good. Okay. Cursor, plot code. Okay. If you it's kind of hard to make, like, cursor work on these. Maybe use, like, CodeGen has an API. Okay? You can use that API. It's pretty cheap. And then yeah, clot code. You don't need to do so many first of all, make sure you don't spend much. Like, max, spend, like, like, I don't know, $100. You know? And then I I will just give you back that money. Whatever it Okay. Okay. Max max spend that. You don't need to run full SuiVenture Lite. That's, like, a thousand dollars. I see. You get me? So if you even run, like, 20, 30 examples, and then you have it set up so that if you run, like, I don't know, SWE bench dash dash Claude. Sweet bench dash dash codex, Okay. By the way, codex, you do have access. You can use the CLI. Yeah. Okay? You know the c the oh my god. My phone's gonna Wait. Oh, shit. I haven't used scope. Codex before. Yeah. Okay. Okay. So, basically, basically, all you need to do is so Codex has a CLI. Right? Yeah. What you need to do is also, did you use Landgraf a lot? Just Jupyter Notebook, like, not in productions. Okay. That's fine. You you but you use Landgraf. You're familiar with it? Use Landgraf. Bro, like, how how how familiar are you with it? Yeah. I'm quite familiar. It is a great way to build a genetic system. Okay. Okay. So if you can get codec CLI working in a sandbox, let's say, E2B, So e to b sandbox. Codex and e to b. Clot code, n e to b. And then maybe CodeGen. CodeGen already works as an API. You don't need to do anything. Okay. All you need to do is pass in a prompt, and then it will give you the output. And it'll actually make a pull request for you or something. So see actually, it may not be possible with CodeGen. I don't know. So you have to look at that. Maybe for now, do cloud code. And codex. These two, And then make it work on SWE bench. For, like, 20 to 30 examples. Yeah. So then Let's see. What what does a, like, get? Right? And Yeah. See what result you get. Okay. And then if you can make a conclusion that clot code is better at codex, oh, that's golden. You get me? Okay. Then I can I can, like, literally go to Roland, be like, hey? This guy already did sweet Benchy bells. He's the perfect guy. Okay. And then and then it's an easy hire. Easy hire. Okay, Okay. Because because then nobody is gonna nobody's gonna do this take home yet. You're gonna be, like, the first one. I I really appreciate you believed in me. Let's try my best, bro. And Okay, bro. Oh, bro. Yes. Yes. Do you think I should come to the office to work, or I I should work from home? Come to the office. I like, I am there. You know? So you can come anytime. You can come But thanks for the clarifying the things. I didn't know what you guys doing. We were like, I didn't know that you guys are building site engineering, but I now got what you guys doing right now. Yes, bro. That's that's basically it. We are running cloud code in E2B. Oh, E2B for the sandbox. Yes. So Yes. So so if you can do that, and then after that, run you know, tasks from Suitebench. Okay. Perfect. Okay. So end goal is, like, you you're in the terminal, or you run a Python file, and then you can configure some stuff. Let's say, SWE bench dash dash clot code dash dash 10 examples. Right? Okay. Or bench dash dash codex does does 10 examples or 20 examples. And it runs and it runs, like, let's say, parallelly as well. You can have, like, five or 10 workers. In E2V. Actually, yeah. So you'll have a sandbox. But then you need to run this you need to run these emails somewhere. Maybe if you find a good place to run these emails, that's also great. You can either run it in a Docker container. Okay. Or you can run it in model functions. They should have harnesses for this already. So you can just look at Suibench and see what they're using. You can run them in, like, you can deploy them somewhere and run them run them somewhere, or you can use model. So Okay. Model, I think, is for the deployment of LLM application. Right? Yeah. But what you can so model has functions. Right? You can deploy a modal function. Okay. And then, basically, let's say you run five workers. Right? Yeah. This let's say let's say your function includes like, you extracting the input and then you're running the clot code in a sandbox Yeah. And then you're getting the output. That's inside the function. Right? Okay. You can run this model function, like, five times, 10 times per task. Do you get me? Yeah. Yeah. So you can basically, you have a sandbox, but you need to you need you need to figure out the best way to actually run these evals as well. You can either run them in the Docker container or you can run them in modal. Yeah. Just make it as easy as possible. Okay. Yeah. Sure. Are are you guys coming tomorrow? I will come tomorrow, of course. Okay. See you see you on at at What's your Can you give me a job? I can clean the clean offices. I get I get police officers very well. Bro, Okay. Okay, bro. See you tomorrow. A good night. I'm not joking. Bro.

================
File: Makefile
================
# Makefile for SWE-bench AI Evaluator

.PHONY: help install test lint format clean setup run-basic run-comparison deploy-modal

# Default target
help:
	@echo "SWE-bench AI Evaluator - Available Commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  install          Install dependencies"
	@echo "  setup            Set up environment and check configuration"
	@echo ""
	@echo "Development:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  lint             Run code linting"
	@echo "  format           Format code with black"
	@echo "  type-check       Run type checking with mypy"
	@echo ""
	@echo "Evaluation:"
	@echo "  run-basic        Run basic functionality test"
	@echo "  run-small        Run small evaluation (5 tasks, Claude)"
	@echo "  run-comparison   Run comparison evaluation (20 tasks, all models)"
	@echo "  run-large        Run large evaluation (50 tasks, all models)"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-modal     Deploy to Modal for cloud execution"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean            Clean up generated files"
	@echo "  clean-cache      Clean dataset cache"
	@echo "  clean-results    Clean result files"

# Installation and setup
install:
	pip install -r requirements.txt
	pip install -e .

install-dev:
	pip install -r requirements.txt
	pip install -e ".[dev]"

setup:
	@echo "Setting up SWE-bench AI Evaluator..."
	python cli.py setup
	@echo "Running basic functionality tests..."
	python test_basic_functionality.py

# Testing
test: test-unit test-integration

test-unit:
	pytest tests/ -m "not integration" -v

test-integration:
	pytest tests/ -m "integration" -v --timeout=300

test-basic:
	python test_basic_functionality.py

test-coverage:
	pytest --cov=src --cov-report=html --cov-report=term tests/

# Code quality
lint:
	flake8 src/ tests/ cli.py --max-line-length=100
	
format:
	black src/ tests/ cli.py modal_deployment.py test_basic_functionality.py

format-check:
	black --check src/ tests/ cli.py modal_deployment.py test_basic_functionality.py

type-check:
	mypy src/ --ignore-missing-imports

quality: format lint type-check

# Evaluation commands
run-basic:
	python test_basic_functionality.py

run-small:
	python cli.py evaluate --model claude --examples 5 --budget 10.0

run-comparison:
	python cli.py evaluate --model all --examples 20 --workers 3 --budget 50.0

run-large:
	python cli.py evaluate --model all --examples 50 --workers 5 --budget 100.0

run-django:
	python cli.py evaluate --model claude --examples 15 --repos "django/django" --budget 30.0

# Dataset operations
dataset-info:
	python cli.py info --subset lite

dataset-info-verified:
	python cli.py info --subset verified

# Modal deployment
deploy-modal:
	@echo "Deploying to Modal..."
	@echo "Make sure you have set up Modal secrets:"
	@echo "  modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key"
	@echo "  modal secret create openai-api-key OPENAI_API_KEY=your_key"
	@echo "  modal secret create e2b-api-key E2B_API_KEY=your_key"
	modal deploy modal_deployment.py

run-modal-small:
	modal run modal_deployment.py --subset lite --max-tasks 10 --evaluators claude,codex --workers 3

run-modal-large:
	modal run modal_deployment.py --subset lite --max-tasks 100 --evaluators claude,codex --workers 10

# Cleaning
clean: clean-cache clean-results clean-logs clean-temp

clean-cache:
	rm -rf data/swe_bench/swe_bench_cache.json
	@echo "Dataset cache cleaned"

clean-results:
	rm -rf results/*.json
	@echo "Result files cleaned"

clean-logs:
	rm -rf logs/*.log
	@echo "Log files cleaned"

clean-temp:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/
	@echo "Temporary files cleaned"

# Development helpers
dev-setup: install-dev setup
	@echo "Development environment ready!"

check-env:
	@echo "Checking environment variables..."
	@python -c "from src.config import config; print('✅ Configuration loaded successfully')"

# Docker operations (if using Docker instead of E2B)
docker-build:
	docker build -t swe-bench-evaluator .

docker-run:
	docker run --env-file .env -v $(PWD)/results:/app/results swe-bench-evaluator

# Monitoring and analysis
analyze-results:
	@echo "Available result files:"
	@ls -la results/*.json 2>/dev/null || echo "No result files found"

tail-logs:
	tail -f logs/evaluation.log

# Performance testing
perf-test:
	python -m cProfile -o profile.stats cli.py evaluate --model claude --examples 3
	python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(20)"

# Release preparation
pre-commit: format lint type-check test
	@echo "Pre-commit checks passed!"

release-check: pre-commit test-coverage
	@echo "Release checks completed!"

# Help for specific commands
help-evaluate:
	python cli.py evaluate --help

help-modal:
	@echo "Modal Deployment Help:"
	@echo ""
	@echo "1. Install Modal: pip install modal"
	@echo "2. Set up secrets:"
	@echo "   modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key"
	@echo "   modal secret create openai-api-key OPENAI_API_KEY=your_key"
	@echo "   modal secret create e2b-api-key E2B_API_KEY=your_key"
	@echo "3. Deploy: make deploy-modal"
	@echo "4. Run: make run-modal-small"

# Quick start sequence
quickstart: install setup run-basic run-small
	@echo ""
	@echo "🎉 Quickstart completed successfully!"
	@echo ""
	@echo "Next steps:"
	@echo "  - Run larger evaluation: make run-comparison"
	@echo "  - Deploy to Modal: make help-modal"
	@echo "  - Check results: make analyze-results"

================
File: modal_deployment.py
================
"""Modal deployment for parallel SWE-bench evaluation."""

import modal
import asyncio
from typing import List, Dict, Any
import json
import os

# Create Modal app
app = modal.App("swe-bench-evaluator")

# Define the container image with all dependencies
image = (
    modal.Image.debian_slim()
    .pip_install([
        "anthropic>=0.40.0",
        "openai>=1.50.0",
        "requests>=2.31.0",
        "aiohttp>=3.9.0",
        "asyncio-throttle>=1.0.2",
        "e2b>=0.17.0",
        "docker>=7.0.0",
        "pandas>=2.1.0",
        "numpy>=1.24.0",
        "pydantic>=2.5.0",
        "click>=8.1.0",
        "rich>=13.7.0",
        "python-dotenv>=1.0.0",
        "pyyaml>=6.0.1",
        "pytest>=7.4.0",
        "pytest-asyncio>=0.21.0",
        "structlog>=23.2.0"
    ])
    .apt_install(["git"])
)

# Define secrets for API keys
secrets = [
    modal.Secret.from_name("anthropic-api-key"),
    modal.Secret.from_name("openai-api-key"),
    modal.Secret.from_name("e2b-api-key")
]


@app.function(
    image=image,
    secrets=secrets,
    timeout=1800,  # 30 minutes timeout
    memory=2048,   # 2GB memory
    cpu=2.0        # 2 CPU cores
)
async def evaluate_single_task_modal(task_data: Dict[str, Any], 
                                   evaluator_type: str) -> Dict[str, Any]:
    """Evaluate a single task in Modal."""
    
    # Import here to avoid issues with Modal
    import sys
    sys.path.append('/root')
    
    from src.models import SWEBenchTask, EvaluatorType
    from src.evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
    from src.swe_bench import TestRunner
    from src.config import config
    
    # Override config with environment variables
    config.anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")
    config.openai_api_key = os.environ.get("OPENAI_API_KEY")
    config.e2b_api_key = os.environ.get("E2B_API_KEY")
    
    try:
        # Parse task data
        task = SWEBenchTask(**task_data)
        evaluator_type_enum = EvaluatorType(evaluator_type)
        
        # Create evaluator
        evaluators = {
            EvaluatorType.CLAUDE: ClaudeEvaluator(),
            EvaluatorType.CODEX: CodexEvaluator(),
            EvaluatorType.CODEGEN: CodeGenEvaluator()
        }
        
        evaluator = evaluators[evaluator_type_enum]
        
        # Evaluate task
        result = await evaluator.evaluate_task(task)
        
        # If solution was generated, run tests
        if result.solution:
            test_runner = TestRunner(use_e2b=True)
            validation_results = await test_runner.validate_solution(
                task, result.solution
            )
            
            # Update result with test information
            result.test_results = validation_results.get("test_results", [])
            result.tests_passed = validation_results.get("passed_tests", 0)
            result.tests_failed = validation_results.get("failed_tests", 0)
        
        # Convert result to dictionary for serialization
        result_dict = result.dict()
        
        # Convert datetime objects to strings
        if result_dict.get('started_at'):
            result_dict['started_at'] = result_dict['started_at'].isoformat()
        if result_dict.get('completed_at'):
            result_dict['completed_at'] = result_dict['completed_at'].isoformat()
        
        return result_dict
        
    except Exception as e:
        # Return error result
        return {
            "task_id": task_data.get("instance_id", "unknown"),
            "evaluator_type": evaluator_type,
            "status": "failed",
            "error_message": str(e)
        }


@app.function(
    image=image,
    secrets=secrets,
    timeout=3600,  # 1 hour timeout
    memory=1024    # 1GB memory
)
async def evaluate_batch_modal(tasks_data: List[Dict[str, Any]], 
                             evaluator_type: str,
                             max_concurrent: int = 5) -> List[Dict[str, Any]]:
    """Evaluate a batch of tasks in Modal with concurrency control."""
    
    # Create semaphore for concurrency control
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def evaluate_with_semaphore(task_data):
        async with semaphore:
            return await evaluate_single_task_modal.remote(task_data, evaluator_type)
    
    # Execute all tasks concurrently
    results = await asyncio.gather(
        *[evaluate_with_semaphore(task_data) for task_data in tasks_data],
        return_exceptions=True
    )
    
    # Handle exceptions
    final_results = []
    for i, result in enumerate(results):
        if isinstance(result, Exception):
            error_result = {
                "task_id": tasks_data[i].get("instance_id", "unknown"),
                "evaluator_type": evaluator_type,
                "status": "failed",
                "error_message": str(result)
            }
            final_results.append(error_result)
        else:
            final_results.append(result)
    
    return final_results


@app.function(
    image=image,
    secrets=secrets,
    timeout=7200,  # 2 hours timeout
    memory=2048    # 2GB memory
)
async def run_full_evaluation_modal(subset: str = "lite",
                                  max_tasks: int = 20,
                                  evaluator_types: List[str] = None,
                                  max_concurrent: int = 3) -> Dict[str, Any]:
    """Run full evaluation in Modal."""
    
    if evaluator_types is None:
        evaluator_types = ["claude", "codex"]
    
    # Import here to avoid Modal issues
    import sys
    sys.path.append('/root')
    
    from src.swe_bench import SWEBenchLoader
    from src.models import EvaluatorType
    
    # Override config
    from src.config import config
    config.anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY")
    config.openai_api_key = os.environ.get("OPENAI_API_KEY")
    config.e2b_api_key = os.environ.get("E2B_API_KEY")
    
    try:
        # Load tasks
        loader = SWEBenchLoader()
        tasks = await loader.load_tasks(subset=subset, max_tasks=max_tasks)
        
        if not tasks:
            return {"error": "No tasks loaded"}
        
        # Convert tasks to serializable format
        tasks_data = [task.dict() for task in tasks]
        
        # Run evaluation for each evaluator type
        all_results = {}
        
        for evaluator_type in evaluator_types:
            print(f"Evaluating with {evaluator_type}...")
            
            # Split tasks into batches for better resource management
            batch_size = 10
            batches = [tasks_data[i:i+batch_size] for i in range(0, len(tasks_data), batch_size)]
            
            evaluator_results = []
            for batch in batches:
                batch_results = await evaluate_batch_modal.remote(
                    batch, evaluator_type, max_concurrent
                )
                evaluator_results.extend(batch_results)
            
            all_results[evaluator_type] = evaluator_results
        
        return {
            "success": True,
            "results": all_results,
            "total_tasks": len(tasks),
            "evaluator_types": evaluator_types
        }
        
    except Exception as e:
        return {
            "success": False,
            "error": str(e)
        }


# Local function to trigger Modal evaluation
@app.local_entrypoint()
def main(subset: str = "lite", 
         max_tasks: int = 20, 
         evaluators: str = "claude,codex",
         workers: int = 3):
    """Local entrypoint to run evaluation on Modal."""
    
    evaluator_list = [e.strip() for e in evaluators.split(',')]
    
    print(f"Starting Modal evaluation...")
    print(f"Subset: {subset}")
    print(f"Max tasks: {max_tasks}")
    print(f"Evaluators: {evaluator_list}")
    print(f"Workers: {workers}")
    
    # Run evaluation
    result = run_full_evaluation_modal.remote(
        subset=subset,
        max_tasks=max_tasks,
        evaluator_types=evaluator_list,
        max_concurrent=workers
    )
    
    if result.get("success"):
        print(f"\n✅ Evaluation completed successfully!")
        print(f"Total tasks: {result['total_tasks']}")
        
        # Save results
        import json
        from datetime import datetime
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"modal_results_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
        
        print(f"Results saved to: {filename}")
        
        # Print summary
        for evaluator_type, results in result["results"].items():
            completed = sum(1 for r in results if r.get("status") == "completed")
            failed = sum(1 for r in results if r.get("status") == "failed")
            total_cost = sum(r.get("api_cost", 0) for r in results)
            
            print(f"\n{evaluator_type.title()} Results:")
            print(f"  Completed: {completed}/{len(results)}")
            print(f"  Failed: {failed}")
            print(f"  Total Cost: ${total_cost:.2f}")
    
    else:
        print(f"❌ Evaluation failed: {result.get('error')}")


if __name__ == "__main__":
    # Example usage:
    # modal run modal_deployment.py --subset lite --max-tasks 10 --evaluators claude,codex --workers 3
    import sys
    
    if len(sys.argv) > 1:
        # Parse command line arguments
        args = {}
        for i in range(1, len(sys.argv), 2):
            if i + 1 < len(sys.argv):
                key = sys.argv[i].lstrip('-')
                value = sys.argv[i + 1]
                
                if key in ['max-tasks', 'workers']:
                    value = int(value)
                
                args[key.replace('-', '_')] = value
        
        main(**args)
    else:
        main()

================
File: pytest.ini
================
[tool:pytest]
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
asyncio_mode = auto

================
File: QUICKSTART.md
================
# Quick Start Guide

Get up and running with the SWE-bench AI Evaluator in 5 minutes.

## Prerequisites

- Python 3.9+
- Git
- API keys for the AI services you want to test

## Installation

1. **Clone and install**:
```bash
git clone <repository-url>
cd swe-bench-evaluator
pip install -r requirements.txt
```

2. **Set up environment**:
```bash
cp .env.example .env
# Edit .env with your API keys
```

3. **Verify setup**:
```bash
python test_basic_functionality.py
```

## API Keys Setup

### Required Keys

Get these API keys and add them to your `.env` file:

1. **Anthropic (Claude)**:
   - Go to https://console.anthropic.com/
   - Create API key
   - Add to `.env`: `ANTHROPIC_API_KEY=your_key_here`

2. **OpenAI (Codex)**:
   - Go to https://platform.openai.com/api-keys
   - Create API key
   - Add to `.env`: `OPENAI_API_KEY=your_key_here`

3. **E2B (Sandboxes)**:
   - Go to https://e2b.dev/
   - Create account and get API key
   - Add to `.env`: `E2B_API_KEY=your_key_here`

### Optional: Codex CLI

For Codex evaluations, install the CLI:
```bash
# Follow OpenAI's Codex CLI installation guide
# This is required for Codex evaluations
```

## First Evaluation

Run your first evaluation with Claude on 5 tasks:

```bash
python cli.py evaluate --model claude --examples 5 --budget 10.0
```

This will:
- Download SWE-bench lite dataset
- Evaluate 5 tasks using Claude
- Run tests in secure sandboxes
- Generate a comparison report
- Stay within $10 budget

## Understanding Results

The evaluation will show:

```
Model     Tasks  Completed  Success Rate  Avg Time  Total Cost
Claude    5      4/5        75.0%         45.2s     $3.45
```

- **Success Rate**: Percentage of tasks where all tests pass correctly
- **Completion Rate**: Tasks completed without errors
- **Cost**: Total API usage cost

## Next Steps

### Compare Multiple Models

```bash
# Compare Claude and Codex
python cli.py evaluate --model all --examples 10 --workers 3
```

### Filter by Repository

```bash
# Only evaluate Django tasks
python cli.py evaluate --model claude --examples 15 --repos "django/django"
```

### Scale Up

```bash
# Larger evaluation
python cli.py evaluate --model all --examples 50 --workers 5 --budget 50.0
```

### Use Modal for Scale

```bash
# Install Modal
pip install modal

# Set up Modal secrets
modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key
modal secret create openai-api-key OPENAI_API_KEY=your_key  
modal secret create e2b-api-key E2B_API_KEY=your_key

# Run on Modal
modal run modal_deployment.py --max-tasks 100 --evaluators claude,codex
```

## Troubleshooting

### Common Issues

1. **"No API key" errors**:
   - Check your `.env` file
   - Ensure keys are valid and have credits

2. **"E2B connection failed"**:
   - Verify E2B API key
   - Check internet connection
   - Try Docker fallback: set `USE_E2B=false` in `.env`

3. **"Codex CLI not found"**:
   - Install Codex CLI from OpenAI
   - Or skip Codex: `--model claude`

4. **Budget exceeded**:
   - Increase budget: `--budget 20.0`
   - Reduce examples: `--examples 10`

### Getting Help

1. **Check logs**: `tail -f logs/evaluation.log`
2. **Run diagnostics**: `python cli.py setup`
3. **Test basic functionality**: `python test_basic_functionality.py`

## Understanding SWE-bench

SWE-bench evaluates AI tools on real GitHub issues:

- **FAIL_TO_PASS tests**: Should fail before fix, pass after
- **PASS_TO_PASS tests**: Should pass both before and after
- **Deterministic**: Based on actual test execution, not AI judgment

Success means the AI correctly implemented the GitHub issue fix.

## Cost Management

Typical costs per task:
- Claude: $0.02-0.10
- Codex: $0.05-0.15  
- CodeGen: $0.03-0.08

Budget recommendations:
- Testing (5-10 tasks): $5-10
- Small comparison (20 tasks): $20-30
- Full evaluation (50+ tasks): $50-100

The framework automatically stops when budget is exceeded.

## What's Next?

Once you have basic evaluations working:

1. **Analyze Results**: Use `python cli.py analyze results_file.json`
2. **Custom Filters**: Filter by specific repositories or issue types
3. **Scale Up**: Use Modal for large-scale evaluations
4. **Integrate**: Use the Python API in your own projects

Happy evaluating! 🚀

================
File: README.md
================
# SWE-bench AI Evaluator

A comprehensive framework for evaluating and comparing AI coding tools (Claude, Codex, CodeGen) on SWE-bench tasks. This tool provides deterministic evaluation using existing test suites rather than LLM-as-judge approaches.

## Features

- **Multiple AI Evaluators**: Support for Claude, Codex CLI, and CodeGen API
- **Deterministic Testing**: Uses SWE-bench's existing test suites for objective evaluation
- **Sandboxed Execution**: Secure code execution using E2B or Docker containers
- **Parallel Processing**: Concurrent evaluation with configurable worker limits
- **Cost Tracking**: Built-in budget management and cost monitoring
- **Rich CLI**: Beautiful command-line interface with progress tracking
- **Modal Deployment**: Scalable cloud deployment option

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone <repository-url>
cd swe-bench-evaluator

# Install dependencies
pip install -r requirements.txt

# Or install in development mode
pip install -e .
```

### 2. Configuration

Copy the example environment file and configure your API keys:

```bash
cp .env.example .env
```

Edit `.env` with your API keys:

```env
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
E2B_API_KEY=your_e2b_api_key_here
```

### 3. Basic Usage

```bash
# Evaluate Claude on 10 tasks
python cli.py evaluate --model claude --examples 10

# Compare Claude and Codex on 20 tasks
python cli.py evaluate --model all --examples 20 --workers 3

# Get dataset information
python cli.py info --subset lite

# Check setup
python cli.py setup
```

## Detailed Usage

### Command Line Interface

#### Evaluate Models

```bash
# Single model evaluation
python cli.py evaluate --model claude --examples 20 --workers 5

# Compare multiple models
python cli.py evaluate --model all --examples 30 --workers 3 --budget 50.0

# Filter by repositories
python cli.py evaluate --model claude --examples 15 --repos "django/django,requests/requests"

# Use different dataset subset
python cli.py evaluate --model codex --examples 10 --subset verified
```

#### Options

- `--model`: AI model to evaluate (`claude`, `codex`, `codegen`, `all`)
- `--examples`: Number of examples to evaluate (default: 20)
- `--workers`: Number of concurrent workers (default: 5)
- `--subset`: SWE-bench dataset subset (`lite`, `verified`, `full`)
- `--repos`: Comma-separated list of repositories to filter by
- `--budget`: Maximum budget in USD (default: 100.0)

### Python API

```python
import asyncio
from src.evaluator import SWEBenchEvaluator
from src.models import EvaluatorType

async def main():
    evaluator = SWEBenchEvaluator()
    
    # Run comparison
    report = await evaluator.run_evaluation(
        subset="lite",
        max_tasks=20,
        evaluator_types=[EvaluatorType.CLAUDE, EvaluatorType.CODEX],
        max_concurrent=3
    )
    
    # Print results
    for evaluator_type, summary in report.summaries.items():
        print(f"{evaluator_type.value}: {summary.overall_success_rate:.1%} success rate")

asyncio.run(main())
```

### Modal Deployment

For large-scale evaluations, deploy to Modal:

```bash
# Install Modal
pip install modal

# Set up Modal secrets
modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key_here
modal secret create openai-api-key OPENAI_API_KEY=your_key_here
modal secret create e2b-api-key E2B_API_KEY=your_key_here

# Run evaluation on Modal
modal run modal_deployment.py --subset lite --max-tasks 50 --evaluators claude,codex --workers 5
```

## Architecture

### Core Components

1. **Evaluators** (`src/evaluators/`): AI tool integrations
   - `ClaudeEvaluator`: Anthropic Claude API integration
   - `CodexEvaluator`: OpenAI Codex CLI integration
   - `CodeGenEvaluator`: CodeGen API integration

2. **SWE-bench Integration** (`src/swe_bench/`):
   - `SWEBenchLoader`: Dataset loading and caching
   - `TestRunner`: Test execution in sandboxes
   - `ResultValidator`: Result validation and analysis

3. **Sandbox Management** (`src/sandbox/`):
   - `E2BSandboxManager`: E2B sandbox integration
   - `DockerSandboxManager`: Docker fallback option

4. **Utilities** (`src/utils/`):
   - Rate limiting and budget tracking
   - Structured logging
   - Configuration management

### Evaluation Process

1. **Task Loading**: Load SWE-bench tasks from official dataset
2. **Solution Generation**: Generate code solutions using AI evaluators
3. **Sandbox Setup**: Create isolated environment with repository
4. **Test Execution**: Run baseline tests, apply solution, run tests again
5. **Result Validation**: Validate results against SWE-bench criteria
6. **Analysis**: Generate comparison reports and metrics

### SWE-bench Evaluation Criteria

- **FAIL_TO_PASS tests**: Should fail before solution, pass after
- **PASS_TO_PASS tests**: Should pass both before and after solution
- **Deterministic**: Based on actual test execution, not LLM judgment

## Configuration

### Environment Variables

```env
# API Keys
ANTHROPIC_API_KEY=your_anthropic_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
E2B_API_KEY=your_e2b_api_key_here

# Evaluation Settings
MAX_BUDGET=100.0
DEFAULT_WORKERS=5
DEFAULT_EXAMPLES=20

# Paths
SWE_BENCH_DATASET_PATH=./data/swe_bench
RESULTS_OUTPUT_PATH=./results

# Logging
LOG_LEVEL=INFO
LOG_FILE=./logs/evaluation.log
```

### Rate Limits

- Claude: 50 requests/minute
- OpenAI: 60 requests/minute
- Budget tracking with automatic stopping

## Results and Analysis

### Output Files

- `results/evaluation_results_*.json`: Raw evaluation results
- `results/evaluation_summary_*.json`: Aggregated summaries
- `results/comparison_report_*.json`: Multi-evaluator comparisons
- `logs/evaluation.log`: Detailed execution logs

### Metrics

- **Completion Rate**: Percentage of tasks completed successfully
- **Success Rate**: Percentage of tests passing (SWE-bench criteria)
- **Cost Efficiency**: Cost per successful test
- **Execution Time**: Average time per task

## Development

### Running Tests

```bash
# Install development dependencies
pip install -e ".[dev]"

# Run tests
pytest tests/

# Run with coverage
pytest --cov=src tests/
```

### Code Quality

```bash
# Format code
black src/ tests/

# Lint code
flake8 src/ tests/

# Type checking
mypy src/
```

## Troubleshooting

### Common Issues

1. **API Key Errors**: Ensure all API keys are set in `.env` file
2. **E2B Connection Issues**: Check E2B API key and network connectivity
3. **Docker Issues**: Ensure Docker is installed and running
4. **Budget Exceeded**: Increase budget limit or reduce number of examples
5. **Codex CLI Not Found**: Install Codex CLI from OpenAI

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=DEBUG
python cli.py evaluate --model claude --examples 5
```

### Support

For issues and questions:
1. Check the logs in `logs/evaluation.log`
2. Review the troubleshooting section
3. Open an issue on GitHub

## License

MIT License - see LICENSE file for details.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Acknowledgments

- [SWE-bench](https://github.com/princeton-nlp/SWE-bench) for the evaluation dataset
- [E2B](https://e2b.dev/) for secure code execution sandboxes
- [Modal](https://modal.com/) for scalable cloud deployment

================
File: requirements.txt
================
# Core dependencies
anthropic>=0.40.0
openai>=1.50.0
requests>=2.31.0
aiohttp>=3.9.0
asyncio-throttle>=1.0.2

# Sandbox and execution
e2b>=0.17.0
docker>=7.0.0

# Data processing
pandas>=2.1.0
numpy>=1.24.0
pydantic>=2.5.0

# Parallel processing
modal>=0.64.0

# CLI and utilities
click>=8.1.0
rich>=13.7.0
python-dotenv>=1.0.0
pyyaml>=6.0.1

# Testing and validation
pytest>=7.4.0
pytest-asyncio>=0.21.0

# Logging and monitoring
structlog>=23.2.0

================
File: setup.py
================
"""Setup script for SWE-bench AI Evaluator."""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="swe-bench-evaluator",
    version="0.1.0",
    author="AI Code Evaluation Team",
    author_email="<EMAIL>",
    description="Compare AI coding tools on SWE-bench tasks",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/example/swe-bench-evaluator",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.9",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "swe-bench-eval=cli:cli",
        ],
    },
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.0.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
    },
)

================
File: test_basic_functionality.py
================
#!/usr/bin/env python3
"""Basic functionality test script for SWE-bench evaluator."""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.models import SWEBenchTask, EvaluatorType
from src.swe_bench import SWEBenchLoader
from src.config import config
from src.utils import logger


async def test_task_loading():
    """Test SWE-bench task loading."""
    print("🔄 Testing SWE-bench task loading...")
    
    try:
        loader = SWEBenchLoader()
        
        # Try to load a small number of tasks
        tasks = await loader.load_tasks(subset="lite", max_tasks=5)
        
        if tasks:
            print(f"✅ Successfully loaded {len(tasks)} tasks")
            
            # Show task statistics
            stats = loader.get_task_statistics(tasks)
            print(f"   - Unique repositories: {stats['unique_repos']}")
            print(f"   - Tasks with tests: {stats['tasks_with_tests']}")
            
            # Show first task details
            first_task = tasks[0]
            print(f"   - Sample task: {first_task.instance_id}")
            print(f"   - Repository: {first_task.repo}")
            print(f"   - Problem length: {len(first_task.problem_statement)} chars")
            
            return True
        else:
            print("❌ No tasks loaded")
            return False
            
    except Exception as e:
        print(f"❌ Task loading failed: {str(e)}")
        return False


def test_evaluator_initialization():
    """Test evaluator initialization."""
    print("🔄 Testing evaluator initialization...")
    
    try:
        from src.evaluators import ClaudeEvaluator, CodexEvaluator, CodeGenEvaluator
        
        # Test Claude evaluator (requires API key)
        if config.anthropic_api_key and config.anthropic_api_key != "your_anthropic_api_key_here":
            claude = ClaudeEvaluator()
            print(f"✅ Claude evaluator initialized (rate limit: {claude.get_rate_limit()}/min)")
        else:
            print("⚠️  Claude evaluator skipped (no API key)")
        
        # Test Codex evaluator
        codex = CodexEvaluator()
        print(f"✅ Codex evaluator initialized (rate limit: {codex.get_rate_limit()}/min)")
        
        # Test CodeGen evaluator
        codegen = CodeGenEvaluator()
        print(f"✅ CodeGen evaluator initialized (rate limit: {codegen.get_rate_limit()}/min)")
        
        return True
        
    except Exception as e:
        print(f"❌ Evaluator initialization failed: {str(e)}")
        return False


def test_cost_estimation():
    """Test cost estimation."""
    print("🔄 Testing cost estimation...")
    
    try:
        from src.evaluators import ClaudeEvaluator
        
        # Create sample task
        sample_task = SWEBenchTask(
            instance_id="test_task",
            repo="test/repo",
            base_commit="abc123",
            problem_statement="Fix the bug in the function that processes user input",
            hints_text="Check the error handling and validation logic",
            created_at="2024-01-01",
            version="1.0",
            FAIL_TO_PASS=["test_function_fix"],
            PASS_TO_PASS=["test_existing_functionality"]
        )
        
        # Test cost estimation
        if config.anthropic_api_key and config.anthropic_api_key != "your_anthropic_api_key_here":
            claude = ClaudeEvaluator()
            cost = claude.estimate_cost(sample_task)
            print(f"✅ Claude cost estimation: ${cost:.4f}")
            
            if cost > 0 and cost < 1.0:  # Reasonable range
                print("   - Cost estimate looks reasonable")
            else:
                print("   - Warning: Cost estimate may be off")
        else:
            print("⚠️  Cost estimation skipped (no API key)")
        
        return True
        
    except Exception as e:
        print(f"❌ Cost estimation failed: {str(e)}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("🔄 Testing configuration...")
    
    try:
        print(f"✅ Configuration loaded")
        print(f"   - Max budget: ${config.max_budget}")
        print(f"   - Default workers: {config.default_workers}")
        print(f"   - Default examples: {config.default_examples}")
        print(f"   - Dataset path: {config.swe_bench_dataset_path}")
        print(f"   - Results path: {config.results_output_path}")
        
        # Check if directories exist
        if config.swe_bench_dataset_path.exists():
            print("   - Dataset directory exists")
        else:
            print("   - Dataset directory created")
        
        if config.results_output_path.exists():
            print("   - Results directory exists")
        else:
            print("   - Results directory created")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False


def test_logging():
    """Test logging functionality."""
    print("🔄 Testing logging...")
    
    try:
        from src.utils import eval_logger
        
        # Test basic logging
        logger.info("Test log message", test=True)
        eval_logger.task_started("test_task", "claude")
        eval_logger.task_completed("test_task", "claude", 0.8, 10.0, 0.05)
        
        print("✅ Logging system working")
        return True
        
    except Exception as e:
        print(f"❌ Logging test failed: {str(e)}")
        return False


def test_budget_tracking():
    """Test budget tracking."""
    print("🔄 Testing budget tracking...")
    
    try:
        from src.utils import budget_tracker
        
        # Reset budget tracker
        budget_tracker.max_budget = 10.0
        budget_tracker.current_cost = 0.0
        budget_tracker.cost_by_service = {}
        
        # Test budget operations
        assert budget_tracker.can_afford(5.0) is True
        assert budget_tracker.can_afford(15.0) is False
        
        budget_tracker.add_cost("claude", 3.0)
        assert budget_tracker.current_cost == 3.0
        assert budget_tracker.get_remaining_budget() == 7.0
        
        budget_tracker.add_cost("codex", 2.0)
        assert budget_tracker.current_cost == 5.0
        
        usage_pct = budget_tracker.get_budget_usage_percentage()
        assert usage_pct == 50.0
        
        print("✅ Budget tracking working")
        print(f"   - Current cost: ${budget_tracker.current_cost}")
        print(f"   - Usage: {usage_pct}%")
        print(f"   - Cost breakdown: {budget_tracker.get_cost_breakdown()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Budget tracking test failed: {str(e)}")
        return False


async def main():
    """Run all basic functionality tests."""
    print("🚀 Starting SWE-bench Evaluator Basic Functionality Tests\n")
    
    tests = [
        ("Configuration", test_configuration),
        ("Logging", test_logging),
        ("Budget Tracking", test_budget_tracking),
        ("Evaluator Initialization", test_evaluator_initialization),
        ("Cost Estimation", test_cost_estimation),
        ("Task Loading", test_task_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running: {test_name}")
        print('='*50)
        
        if asyncio.iscoroutinefunction(test_func):
            result = await test_func()
        else:
            result = test_func()
        
        results.append((test_name, result))
    
    # Summary
    print(f"\n{'='*50}")
    print("TEST SUMMARY")
    print('='*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic functionality tests passed!")
        print("\nNext steps:")
        print("1. Set up your API keys in .env file")
        print("2. Run: python cli.py setup")
        print("3. Run: python cli.py evaluate --model claude --examples 5")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
