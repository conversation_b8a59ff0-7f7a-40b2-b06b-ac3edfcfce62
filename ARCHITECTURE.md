# Architecture Documentation

This document describes the architecture and design decisions of the SWE-bench AI Evaluator.

## Overview

The SWE-bench AI Evaluator is designed as a modular, scalable framework for comparing AI coding tools on real-world software engineering tasks. It follows the SWE-bench evaluation methodology using deterministic test-based validation.

## Core Principles

1. **Deterministic Evaluation**: Use actual test execution rather than LLM-as-judge
2. **Modular Design**: Easy to add new AI evaluators and sandbox providers
3. **Cost Awareness**: Built-in budget tracking and rate limiting
4. **Scalability**: Support for both local and cloud-based parallel execution
5. **Reproducibility**: Comprehensive logging and result serialization

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                     CLI Interface                           │
│                   (cli.py)                                  │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                Main Evaluator                               │
│              (src/evaluator.py)                             │
│  • Orchestrates evaluation pipeline                        │
│  • Manages concurrent execution                             │
│  • Coordinates components                                   │
└─────┬───────────────┬───────────────┬─────────────────────┬─┘
      │               │               │                     │
┌─────▼─────┐  ┌─────▼─────┐  ┌─────▼─────┐  ┌─────────▼─────┐
│Evaluators │  │SWE-bench  │  │ Sandbox   │  │   Utilities   │
│           │  │Integration│  │ Managers  │  │               │
│• Claude   │  │           │  │           │  │• Logging      │
│• Codex    │  │• Loader   │  │• E2B      │  │• Rate Limit   │
│• CodeGen  │  │• Runner   │  │• Docker   │  │• Budget Track │
│           │  │• Validator│  │           │  │• Config       │
└───────────┘  └───────────┘  └───────────┘  └───────────────┘
```

## Component Details

### 1. Evaluators (`src/evaluators/`)

**Purpose**: Interface with different AI coding tools

**Components**:
- `BaseEvaluator`: Abstract base class defining the evaluator interface
- `ClaudeEvaluator`: Anthropic Claude API integration
- `CodexEvaluator`: OpenAI Codex CLI integration  
- `CodeGenEvaluator`: CodeGen API integration

**Key Features**:
- Async/await for non-blocking API calls
- Built-in rate limiting and cost estimation
- Standardized solution format
- Error handling and retry logic

**Design Pattern**: Strategy pattern for interchangeable AI evaluators

### 2. SWE-bench Integration (`src/swe_bench/`)

**Purpose**: Handle SWE-bench dataset and evaluation logic

**Components**:
- `SWEBenchLoader`: Download, cache, and parse SWE-bench tasks
- `TestRunner`: Execute tests in sandboxed environments
- `ResultValidator`: Validate results against SWE-bench criteria

**Key Features**:
- Automatic dataset downloading and caching
- Deterministic test evaluation (FAIL_TO_PASS, PASS_TO_PASS)
- Comprehensive result validation
- Support for different dataset subsets

### 3. Sandbox Managers (`src/sandbox/`)

**Purpose**: Provide secure, isolated code execution environments

**Components**:
- `E2BSandboxManager`: E2B cloud sandbox integration
- `DockerSandboxManager`: Local Docker container management

**Key Features**:
- Repository cloning and setup
- Dependency installation
- Code application and test execution
- Automatic cleanup

**Design Pattern**: Strategy pattern with fallback (E2B → Docker)

### 4. Utilities (`src/utils/`)

**Purpose**: Cross-cutting concerns and shared functionality

**Components**:
- `logger.py`: Structured logging with evaluation-specific events
- `rate_limiter.py`: API rate limiting and budget tracking
- `config.py`: Configuration management with environment variables

## Data Models (`src/models.py`)

The system uses Pydantic models for type safety and validation:

```python
SWEBenchTask → CodeSolution → TestResult → EvaluationResult → EvaluationSummary → ComparisonReport
```

**Key Models**:
- `SWEBenchTask`: Represents a single SWE-bench evaluation task
- `CodeSolution`: AI-generated solution with metadata
- `EvaluationResult`: Complete result of evaluating one task
- `ComparisonReport`: Aggregated comparison across evaluators

## Evaluation Pipeline

### 1. Task Loading
```
SWE-bench Dataset → Cache Check → Download/Parse → Filter → Task List
```

### 2. Solution Generation
```
Task → AI Evaluator → Rate Limit → API Call → Parse Response → CodeSolution
```

### 3. Test Execution
```
Task + Solution → Sandbox → Repository Setup → Baseline Tests → Apply Solution → Final Tests → TestResults
```

### 4. Validation
```
TestResults → SWE-bench Criteria → Success/Failure → Metrics → EvaluationResult
```

### 5. Aggregation
```
EvaluationResults → Summary Statistics → Comparison → Report Generation
```

## Concurrency Model

The system uses Python's `asyncio` for concurrent execution:

- **Task Level**: Multiple tasks evaluated concurrently
- **Evaluator Level**: Different AI tools run in parallel
- **Rate Limiting**: Per-service semaphores prevent API overload
- **Resource Management**: Sandbox cleanup and error handling

```python
# Concurrency structure
Semaphore(max_concurrent_tasks)
├── Task 1 → Evaluator A → Sandbox → Tests
├── Task 2 → Evaluator B → Sandbox → Tests  
└── Task N → Evaluator C → Sandbox → Tests
```

## Configuration Management

**Hierarchy** (highest to lowest priority):
1. Command-line arguments
2. Environment variables
3. `.env` file
4. Default values

**Key Configuration Areas**:
- API credentials and endpoints
- Rate limits and timeouts
- Budget constraints
- File paths and directories
- Logging levels

## Error Handling Strategy

**Levels of Error Handling**:

1. **Task Level**: Individual task failures don't stop the evaluation
2. **Evaluator Level**: API errors are caught and logged
3. **Sandbox Level**: Cleanup always occurs, even on failure
4. **System Level**: Budget and rate limit enforcement

**Error Recovery**:
- Automatic retries for transient failures
- Graceful degradation (E2B → Docker)
- Comprehensive error logging
- Partial result preservation

## Scalability Considerations

### Local Scaling
- Configurable concurrency limits
- Memory-efficient streaming
- Incremental result saving
- Resource cleanup

### Cloud Scaling (Modal)
- Serverless function deployment
- Automatic scaling based on load
- Distributed task execution
- Cost optimization

## Security Considerations

### Sandbox Security
- Isolated execution environments
- No network access from sandboxes
- Temporary file cleanup
- Resource limits

### API Security
- Secure credential storage
- Rate limiting to prevent abuse
- Budget limits to prevent runaway costs
- Input validation and sanitization

## Performance Optimizations

### Caching
- SWE-bench dataset caching
- API response caching (where appropriate)
- Compiled regex patterns
- Configuration caching

### Resource Management
- Connection pooling for HTTP clients
- Lazy loading of large datasets
- Streaming for large files
- Memory-mapped files for results

### Parallel Processing
- Async/await throughout
- Configurable worker pools
- Load balancing across evaluators
- Batch processing optimizations

## Monitoring and Observability

### Logging
- Structured JSON logging
- Multiple log levels
- Evaluation-specific events
- Performance metrics

### Metrics
- Success rates and completion rates
- Cost tracking and budget usage
- Execution time distributions
- Error rates and types

### Debugging
- Comprehensive error messages
- Stack trace preservation
- Request/response logging
- State inspection utilities

## Extension Points

### Adding New Evaluators
1. Inherit from `BaseEvaluator`
2. Implement required methods
3. Add to evaluator factory
4. Update CLI options

### Adding New Sandbox Providers
1. Implement sandbox manager interface
2. Add to sandbox factory
3. Update configuration
4. Add fallback logic

### Adding New Metrics
1. Extend result models
2. Update validation logic
3. Modify report generation
4. Update CLI display

## Testing Strategy

### Unit Tests
- Individual component testing
- Mock external dependencies
- Edge case validation
- Error condition testing

### Integration Tests
- End-to-end pipeline testing
- Real API integration (with limits)
- Sandbox functionality
- Configuration validation

### Performance Tests
- Load testing with multiple tasks
- Memory usage profiling
- Concurrency stress testing
- Cost optimization validation

This architecture provides a solid foundation for reliable, scalable AI code evaluation while maintaining flexibility for future enhancements.
