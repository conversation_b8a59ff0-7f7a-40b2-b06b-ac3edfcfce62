# Makefile for SWE-bench AI Evaluator

.PHONY: help install test lint format clean setup run-basic run-comparison deploy-modal

# Default target
help:
	@echo "SWE-bench AI Evaluator - Available Commands:"
	@echo ""
	@echo "Setup and Installation:"
	@echo "  install          Install dependencies"
	@echo "  setup            Set up environment and check configuration"
	@echo ""
	@echo "Development:"
	@echo "  test             Run all tests"
	@echo "  test-unit        Run unit tests only"
	@echo "  test-integration Run integration tests only"
	@echo "  lint             Run code linting"
	@echo "  format           Format code with black"
	@echo "  type-check       Run type checking with mypy"
	@echo ""
	@echo "Evaluation:"
	@echo "  run-basic        Run basic functionality test"
	@echo "  run-small        Run small evaluation (5 tasks, Claude)"
	@echo "  run-comparison   Run comparison evaluation (20 tasks, all models)"
	@echo "  run-large        Run large evaluation (50 tasks, all models)"
	@echo ""
	@echo "Deployment:"
	@echo "  deploy-modal     Deploy to Modal for cloud execution"
	@echo ""
	@echo "Maintenance:"
	@echo "  clean            Clean up generated files"
	@echo "  clean-cache      Clean dataset cache"
	@echo "  clean-results    Clean result files"

# Installation and setup
install:
	pip install -r requirements.txt
	pip install -e .

install-dev:
	pip install -r requirements.txt
	pip install -e ".[dev]"

setup:
	@echo "Setting up SWE-bench AI Evaluator..."
	python cli.py setup
	@echo "Running basic functionality tests..."
	python test_basic_functionality.py

# Testing
test: test-unit test-integration

test-unit:
	pytest tests/ -m "not integration" -v

test-integration:
	pytest tests/ -m "integration" -v --timeout=300

test-basic:
	python test_basic_functionality.py

test-coverage:
	pytest --cov=src --cov-report=html --cov-report=term tests/

# Code quality
lint:
	flake8 src/ tests/ cli.py --max-line-length=100
	
format:
	black src/ tests/ cli.py modal_deployment.py test_basic_functionality.py

format-check:
	black --check src/ tests/ cli.py modal_deployment.py test_basic_functionality.py

type-check:
	mypy src/ --ignore-missing-imports

quality: format lint type-check

# Evaluation commands
run-basic:
	python test_basic_functionality.py

run-small:
	python cli.py evaluate --model claude --examples 5 --budget 10.0

run-comparison:
	python cli.py evaluate --model all --examples 20 --workers 3 --budget 50.0

run-large:
	python cli.py evaluate --model all --examples 50 --workers 5 --budget 100.0

run-django:
	python cli.py evaluate --model claude --examples 15 --repos "django/django" --budget 30.0

# Dataset operations
dataset-info:
	python cli.py info --subset lite

dataset-info-verified:
	python cli.py info --subset verified

# Modal deployment
deploy-modal:
	@echo "Deploying to Modal..."
	@echo "Make sure you have set up Modal secrets:"
	@echo "  modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key"
	@echo "  modal secret create openai-api-key OPENAI_API_KEY=your_key"
	@echo "  modal secret create e2b-api-key E2B_API_KEY=your_key"
	modal deploy modal_deployment.py

run-modal-small:
	modal run modal_deployment.py --subset lite --max-tasks 10 --evaluators claude,codex --workers 3

run-modal-large:
	modal run modal_deployment.py --subset lite --max-tasks 100 --evaluators claude,codex --workers 10

# Cleaning
clean: clean-cache clean-results clean-logs clean-temp

clean-cache:
	rm -rf data/swe_bench/swe_bench_cache.json
	@echo "Dataset cache cleaned"

clean-results:
	rm -rf results/*.json
	@echo "Result files cleaned"

clean-logs:
	rm -rf logs/*.log
	@echo "Log files cleaned"

clean-temp:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	rm -rf .coverage htmlcov/
	@echo "Temporary files cleaned"

# Development helpers
dev-setup: install-dev setup
	@echo "Development environment ready!"

check-env:
	@echo "Checking environment variables..."
	@python -c "from src.config import config; print('✅ Configuration loaded successfully')"

# Docker operations (if using Docker instead of E2B)
docker-build:
	docker build -t swe-bench-evaluator .

docker-run:
	docker run --env-file .env -v $(PWD)/results:/app/results swe-bench-evaluator

# Monitoring and analysis
analyze-results:
	@echo "Available result files:"
	@ls -la results/*.json 2>/dev/null || echo "No result files found"

tail-logs:
	tail -f logs/evaluation.log

# Performance testing
perf-test:
	python -m cProfile -o profile.stats cli.py evaluate --model claude --examples 3
	python -c "import pstats; p = pstats.Stats('profile.stats'); p.sort_stats('cumulative').print_stats(20)"

# Release preparation
pre-commit: format lint type-check test
	@echo "Pre-commit checks passed!"

release-check: pre-commit test-coverage
	@echo "Release checks completed!"

# Help for specific commands
help-evaluate:
	python cli.py evaluate --help

help-modal:
	@echo "Modal Deployment Help:"
	@echo ""
	@echo "1. Install Modal: pip install modal"
	@echo "2. Set up secrets:"
	@echo "   modal secret create anthropic-api-key ANTHROPIC_API_KEY=your_key"
	@echo "   modal secret create openai-api-key OPENAI_API_KEY=your_key"
	@echo "   modal secret create e2b-api-key E2B_API_KEY=your_key"
	@echo "3. Deploy: make deploy-modal"
	@echo "4. Run: make run-modal-small"

# Quick start sequence
quickstart: install setup run-basic run-small
	@echo ""
	@echo "🎉 Quickstart completed successfully!"
	@echo ""
	@echo "Next steps:"
	@echo "  - Run larger evaluation: make run-comparison"
	@echo "  - Deploy to Modal: make help-modal"
	@echo "  - Check results: make analyze-results"
